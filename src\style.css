:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
}

/* 確保頁面在應用載入前不顯示內容，避免跑版 */
html:not(.app-loaded) .app-content {
  visibility: hidden;
}

/* 修復跑版問題的全局樣式 */
.app-loaded .app-content {
  visibility: visible;
  transition: opacity 0.3s ease;
}

/* 確保彈出視窗正確顯示 */
.modal-dialog {
  max-width: 90%;
  width: auto;
  margin: 1.75rem auto;
}

@media (min-width: 768px) {
  .modal-dialog {
    max-width: 700px;
  }
}

@media (min-width: 992px) {
  .modal-dialog {
    max-width: 900px;
  }
}

/* 確保表單元素正確對齊 */
.form-group {
  margin-bottom: 1rem;
  position: relative;
}

/* 確保輸入框在各種設備上顯示一致 */
input, select, textarea {
  font-size: 16px !important; /* 防止iOS上自動縮放 */
}

/* 修復某些瀏覽器中的佈局問題 */
* {
  box-sizing: border-box;
}

/* 確保頁面內容不會溢出 */
body, html {
  overflow-x: hidden;
  width: 100%;
  height: 100%;
}
