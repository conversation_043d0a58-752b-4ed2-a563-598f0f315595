import { createRouter, createWebHistory } from 'vue-router';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {path: '/', redirect: '/login'},
    {path: '/login', component: () => import('../views/LoginPage.vue'), meta: { requiresAuth: false }},
    {path: '/reserve', component: () => import('../views/ReservePage.vue'), meta: { requiresAuth: true }},
    {path: '/record', component: () => import('../views/RecordPage.vue'), meta: { requiresAuth: true }},
    {path: '/statistics', component: () => import('../views/StatisticsPage.vue'), meta: { requiresAuth: true }},
    {path: '/manage', component: () => import('../views/ManagePage.vue'), meta: { requiresAuth: true }}
  ]
});

// 全局路由守衛
router.beforeEach((to, from, next) => {
  console.log(`路由導航: 從 ${from.path} 到 ${to.path}`);
  
  // 獲取認證狀態
  const token = localStorage.getItem('token');
  const isAuthenticated = !!token;
  
  // 檢查目標路由是否需要認證
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth);
  
  // 處理登入頁面的特殊情況
  if (to.path === '/login') {
    if (isAuthenticated) {
      console.log('已登入，重定向到 /reserve');
      next('/reserve');
    } else {
      console.log('未登入，允許訪問登入頁面');
      next();
    }
    return;
  }
  
  // 處理需要認證的頁面
  if (requiresAuth) {
    if (isAuthenticated) {
      console.log('已登入，允許訪問受保護頁面');
      
      // 檢查是否是從登入頁面導航過來的
      if (from.path === '/login' && to.path === '/reserve') {
        console.log('從登入頁面導航到預約頁面，設置重新載入標記');
        sessionStorage.setItem('forceReload', 'true');
      }
      
      next();
    } else {
      console.log('未登入，重定向到登入頁面');
      next('/login');
    }
    return;
  }
  
  // 其他情況，允許導航
  next();
});

// 全局後置鉤子
router.afterEach((to, from) => {
  console.log(`路由導航完成: 從 ${from.path} 到 ${to.path}`);
  
  // 檢查是否需要強制重新載入
  if (sessionStorage.getItem('forceReload') === 'true' && to.path === '/reserve') {
    console.log('檢測到強制重新載入標記，執行頁面重新載入');
    sessionStorage.removeItem('forceReload');
    
    // 使用 setTimeout 確保路由導航完成後再重新載入
    setTimeout(() => {
      console.log('執行頁面重新載入');
      window.location.reload();
    }, 100);
  }
});

export default router;
