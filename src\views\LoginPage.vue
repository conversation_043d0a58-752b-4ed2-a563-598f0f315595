<template>
    <div class="login-container">
        <div class="rect">
            <img class="loginTitle" src="/src/assets/images/logoWithTxt.png" alt="">
            <div class="content">
                <p class="titleTxt">
                    會議室預約系統
                </p>
                <div v-if="errorMessage" class="text-danger mb-2 text-center">{{ errorMessage }}</div>
                <form @submit.prevent="handleLogin">
                    <div class="form-group mx-1 mb-1">
                        <label class="inputText" for="account">帳號</label>
                        <input type="text" class="form-control" v-model="account" required autocomplete="off">
                    </div>
                    <div class="form-group mx-1 mb-1">
                        <label class="inputText" for="password">密碼</label>
                        <input type="password" class="form-control" v-model="password" required>
                    </div>
                    <div class="login">
                        <button class="btn btn-outline-light rounded-pill btn-rg loginBtn" type="submit">登入</button>
                    </div>
                </form>
            </div>
        </div>
        <div class="copyright">
            Copyright © 2024 Kaohsiung Port Cruise Termial
        </div>
    </div>
    <div v-show="isLoading">
            <div class="fullscreen-overlay"></div>
            <div class="loader text-center"><span style="font-size: 72pt;">Loading</span><br>檢核帳號及密碼中</div>
        </div>
</template>
<style>
@import '../assets/css/login.css';
@import '../assets/css/bootstrap.basic.css';
</style>
<script setup>
import { ref,computed } from 'vue';
import router from '../router';
import api from '../utils/api';
import { useSystemLoadingStore } from '../store/SystemLoadingStore';

const account = ref('');
const password = ref('');
const errorMessage = ref('');
const systemLoadingStore = useSystemLoadingStore()
const isLoading = computed(() => systemLoadingStore.isLoading)

const handleLogin = async () => {
    systemLoadingStore.showDiv();
    errorMessage.value = '';
    
    try {
        const response = await api.post('/api/auth/login', {
            account: account.value,
            password: password.value
        }, {
            callback: function(response) {
                if (response && response.status === 200) {
                    console.log('登入成功:', response.data);
                    
                    // 儲存認證資訊
                    localStorage.setItem('token', response.data.token);
                    localStorage.setItem('userInfo', JSON.stringify(response.data));
                    
                    // 設置登入成功標記
                    sessionStorage.setItem('loginSuccess', 'true');
                    
                    // 檢查當前路由是否已經是 /reserve
                    const currentPath = window.location.pathname;
                    const basePath = import.meta.env.BASE_URL || '/';
                    const normalizedCurrentPath = currentPath.replace(basePath, '/');
                    
                    if (normalizedCurrentPath === '/reserve') {
                        console.log('當前已在 /reserve 頁面，執行頁面重新載入');
                        window.location.reload();
                    } else {
                        console.log('導航到 /reserve 頁面');
                        router.push('/reserve');
                    }
                } else {
                    console.error('登入失敗:', response);
                    errorMessage.value = '帳號或密碼錯誤';
                    systemLoadingStore.hideDiv();
                }
            }
        });
    } catch (error) {
        console.error('登入請求發生錯誤:', error);
        errorMessage.value = '登入過程中發生錯誤，請稍後再試';
        systemLoadingStore.hideDiv();
    }
}
</script>
