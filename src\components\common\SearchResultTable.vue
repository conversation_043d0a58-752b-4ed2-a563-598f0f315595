<template>
    <div class="card bg-transparent border-light rounded-4 p-4 results-card" :class="{'light-card': !localIsDarkMode, 'dark-card': localIsDarkMode}">
        <div class="row">        
            <!-- 搜尋結果區域 -->
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <h5 class="result-title" :class="{'text-light': localIsDarkMode, 'text-dark': !localIsDarkMode}" Style="display: flex; align-items: center;gap: 20px;">
                        <!-- 標題區域 -->
                        <slot name="headerArea">
                            <!-- 默認標題，當父組件沒有提供標題時顯示 -->
                            <span>{{ props.title }}</span>
                        </slot>
                    </h5>
                    <!-- 切換深色/淺色模式按鈕 -->
                    <div class="theme-toggle d-flex align-items-center">
                        <span class="me-2 small" :class="{'text-light': localIsDarkMode, 'text-dark': !localIsDarkMode}">{{ localIsDarkMode ? '深色版' : '淺色版' }}</span>
                        <div class="form-check form-switch mb-0">
                            <input class="form-check-input" type="checkbox" id="themeSwitch" v-model="localIsDarkMode">
                        </div>
                    </div>
                </div>
                <div class="table-responsive search-results-container" :class="{'light-table': !localIsDarkMode, 'dark-table': localIsDarkMode}">
                    <table class="table" :class="{'table-dark': localIsDarkMode, 'table-light': !localIsDarkMode}">
                        <thead>
                            <tr>
                                <th v-for="(column, index) in columns" 
                                    :key="index" 
                                    class="text-secondary" 
                                    :class="{'sortable': column.sortable}"
                                    :data-sorted="column.sortable && (column.sortField || column.field) === sortField.value ? 'true' : null"
                                    :data-field="column.sortField || column.field"
                                    @click="column.sortable && handleSort(column.sortField || column.field)" 
                                    :style="column.width ? {width: column.width} : {}">
                                    {{ column.title }}
                                    <span class="sort-icon" v-if="column.sortable">{{ getSortIcon(column.field) }}</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <template v-if="loading">
                                <tr>
                                    <td :colspan="columns.length" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">載入中...</span>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                            <template v-else-if="tableData.length === 0">
                                <tr>
                                    <td :colspan="columns.length" class="text-center py-4">
                                        無符合條件的資料
                                    </td>
                                </tr>
                            </template>
                            <template v-else>
                                <tr v-for="(data, index) in tableData" :key="index">
                                    <td v-for="(column, index) in columns" :key="index">
                                        <span v-html=" column.render ? column.render(data) : data[column.field] "></span>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分頁控制 -->
                <div class="d-flex justify-content-between align-items-center mt-3" v-if="showPagination">
                    <div class="d-flex align-items-center">
                        <span class="me-2 small" :class="{'text-light': localIsDarkMode, 'text-dark': !localIsDarkMode}">每頁顯示:</span>
                        <select class="form-select form-select-sm" style="width: 70px;" v-model="pageSize" @change="changePageSize">
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span class="ms-3 small" :class="{'text-light': localIsDarkMode, 'text-dark': !localIsDarkMode}">
                            共 {{ totalPages }} 頁（{{ totalItems }} 筆）
                        </span>
                    </div>
                    
                    <nav aria-label="Page navigation">
                        <ul class="pagination pagination-sm mb-0">
                            <!-- 首頁按鈕 -->
                            <li class="page-item" :class="{disabled: currentPage <= 1}">
                                <a class="page-link" href="#" @click.prevent="goToFirstPage" :style="getPageLinkStyle()">
                                    <span>&laquo;</span>
                                </a>
                            </li>
                            <!-- 上一頁按鈕 -->
                            <li class="page-item" :class="{disabled: currentPage <= 1}">
                                <a class="page-link" href="#" @click.prevent="goToPrevPage" :style="getPageLinkStyle()">
                                    <span>&lsaquo;</span>
                                </a>
                            </li>
                            <!-- 頁碼按鈕 -->
                            <li v-for="page in pageNumbers" :key="page" class="page-item" :class="{active: page === currentPage}">
                                <a class="page-link" href="#" @click.prevent="changePage(page)" :style="getPageLinkStyle()">{{ page }}</a>
                            </li>
                            <!-- 下一頁按鈕 -->
                            <li class="page-item" :class="{disabled: currentPage >= totalPages}">
                                <a class="page-link" href="#" @click.prevent="goToNextPage" :style="getPageLinkStyle()">
                                    <span>&rsaquo;</span>
                                </a>
                            </li>
                            <!-- 尾頁按鈕 -->
                            <li class="page-item" :class="{disabled: currentPage >= totalPages}">
                                <a class="page-link" href="#" @click.prevent="goToLastPage" :style="getPageLinkStyle()">
                                    <span>&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>  
    </div>
</template>
<script setup>
import { ref, onMounted, watch, computed, nextTick, defineExpose } from 'vue';
import api from '../../utils/api';

const props = defineProps({
    columns: {
        type: Array,
        required: true
    },
    apiUrl: {
        type: String,
        required: true
    },
    searchParams: {
        type: Object,
        default: () => ({})
    },
    initialSortField: {
        type: String,
        default: ''
    },
    initialSortDirection: {
        type: String,
        default: 'asc'
    },
    showPagination: {
        type: Boolean,
        default: true
    },
    isDarkMode: {
        type: Boolean,
        default: true
    },
    modelValue: {
        type: Boolean,
        default: null
    },
    skipInitialFetch: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['sort', 'themeChange', 'dataLoaded', 'update:isDarkMode', 'update:modelValue']);

// 內部狀態變數
const localIsDarkMode = computed({
    get: () => props.modelValue !== null ? props.modelValue : props.isDarkMode,
    set: (value) => {
        emit('update:isDarkMode', value);
        emit('update:modelValue', value);
        emit('themeChange', value);
        localStorage.setItem('tableTheme', value.toString());
    }
});

// 其他狀態變數
const sortField = ref(props.initialSortField);
const sortDirection = ref(props.initialSortDirection);
const loading = ref(false);
const tableData = ref([]);
const totalItems = ref(0);
const totalPages = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 添加一個標記，表示是否正在手動更新參數
const isUpdatingParams = ref(false);

// 添加一個內部搜索參數狀態
const internalSearchParams = ref({});

// 在組件掛載時初始化
onMounted(() => {
    const savedTheme = localStorage.getItem('tableTheme');
    if (savedTheme !== null && props.modelValue === null) {
        localIsDarkMode.value = savedTheme === 'true';
    }
    
    // 獲取數據，但只在 apiUrl 存在時執行
    if (props.apiUrl && !props.skipInitialFetch) {
        fetchData();
    }
    
    // 初始化排序樣式
    nextTick(() => {
        updateSortHeaderStyle();
    });
});

// 監聽外部 isDarkMode 變化
watch(() => props.isDarkMode, (newValue) => {
    if (props.modelValue === null && localIsDarkMode.value !== newValue) {
        localIsDarkMode.value = newValue;
    }
});

// 監聽外部 modelValue 變化
watch(() => props.modelValue, (newValue) => {
    if (newValue !== null && localIsDarkMode.value !== newValue) {
        localIsDarkMode.value = newValue;
    }
});

// 獲取分頁鏈接樣式 - 移除內聯樣式，使用 CSS 類
const getPageLinkStyle = () => {
    // 返回空對象，讓 CSS 類處理樣式
    return {};
};

// 計算要顯示的頁碼
const pageNumbers = computed(() => {
    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage.value - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;
    
    if (endPage > totalPages.value) {
        endPage = totalPages.value;
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
    }
    
    return pages;
});

// 排序處理
const handleSort = (field) => {
    if (field === sortField.value) {
        // 如果點擊的是當前排序欄位，則切換排序方向
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    } else {
        // 如果點擊的是新欄位，則設置為該欄位升序排序
        sortField.value = field;
        sortDirection.value = 'asc';
    }
    
    // 向父組件發送排序事件
    emit('sort', { field: sortField.value, direction: sortDirection.value });
    
    // 重新載入數據
    fetchData();
    
    // 更新表頭樣式
    updateSortHeaderStyle();
};

// 更新排序表頭樣式
const updateSortHeaderStyle = () => {
    nextTick(() => {
        const headers = document.querySelectorAll('th');
        headers.forEach(header => {
            header.removeAttribute('data-sorted');
        });
        
        const sortedHeader = document.querySelector(`th[data-field="${sortField.value}"]`);
        if (sortedHeader) {
            sortedHeader.setAttribute('data-sorted', 'true');
        }
    });
};

// 獲取排序圖標
const getSortIcon = (field) => {
    if (field !== sortField.value) return '⇅'; // 未排序：雙向箭頭
    return sortDirection.value === 'asc' ? '↑' : '↓'; // 升序：向上箭頭，降序：向下箭頭
};

// 分頁處理
const changePageSize = () => {
    currentPage.value = 1; // 重置為第一頁
    fetchData(); // 重新載入數據
};

const changePage = (page) => {
    if (page < 1 || page > totalPages.value || page === currentPage.value) return;
    currentPage.value = page;
    fetchData(); // 重新載入數據
};

// 跳轉到第一頁
const goToFirstPage = () => {
    if (currentPage.value === 1) return;
    changePage(1);
};

// 跳轉到上一頁
const goToPrevPage = () => {
    if (currentPage.value > 1) {
        changePage(currentPage.value - 1);
    }
};

// 跳轉到下一頁
const goToNextPage = () => {
    if (currentPage.value < totalPages.value) {
        changePage(currentPage.value + 1);
    }
};

// 跳轉到最後一頁
const goToLastPage = () => {
    if (currentPage.value === totalPages.value) return;
    changePage(totalPages.value);
};

// 監聽外部搜索參數變化
watch(() => props.searchParams, (newVal) => {
    // 更新內部搜索參數
    internalSearchParams.value = { ...newVal };
}, { deep: true, immediate: true });

// 獲取數據的方法
const fetchData = async () => {
    // 設置載入狀態，但不清空現有數據
    loading.value = true;
    
    try {
        const params = {
            page: currentPage.value,
            limit: pageSize.value,
            sorts: [{
                field: sortField.value,
                direction: sortDirection.value
            }],
            ...internalSearchParams.value // 使用內部搜索參數
        };
        
        console.log('執行查詢，參數:', params);
        
        await api.post(props.apiUrl, params, {
            callback: function(response) {
                console.log('查詢結果:', response.data);
                if (response.data?.data) {
                    // 準備新數據，但不立即更新
                    const newData = response.data.data.data || [];
                    const newTotal = response.data.data.total || 0;
                    const newTotalPages = response.data.data.totalPage || 0;
                    
                    // 使用單一 nextTick 批量更新所有狀態
                    nextTick(() => {
                        // 批量更新所有狀態
                        tableData.value = newData;
                        totalItems.value = newTotal;
                        totalPages.value = newTotalPages;
                        loading.value = false;
                        
                        // 發送數據加載完成事件
                        emit('dataLoaded', {
                            data: newData,
                            total: newTotal,
                            totalPage: newTotalPages
                        });
                    });
                } else {
                    // 使用單一 nextTick 批量更新所有狀態
                    nextTick(() => {
                        // 批量更新所有狀態
                        tableData.value = [];
                        totalItems.value = 0;
                        totalPages.value = 0;
                        loading.value = false;
                        
                        // 發送數據加載完成事件
                        emit('dataLoaded', {
                            data: [],
                            total: 0,
                            totalPage: 0
                        });
                    });
                }
            },
            error: function(error) {
                console.error('獲取數據失敗:', error);
                // 使用單一 nextTick 批量更新所有狀態
                nextTick(() => {
                    // 批量更新所有狀態
                    tableData.value = [];
                    totalItems.value = 0;
                    totalPages.value = 0;
                    loading.value = false;
                    
                    // 發送數據加載完成事件
                    emit('dataLoaded', {
                        error: error
                    });
                });
            }
        });
    } catch (error) {
        console.error('獲取數據時發生錯誤:', error);
        // 使用單一 nextTick 批量更新所有狀態
        nextTick(() => {
            // 批量更新所有狀態
            tableData.value = [];
            totalItems.value = 0;
            totalPages.value = 0;
            loading.value = false;
            
            // 發送數據加載完成事件
            emit('dataLoaded', {
                error: error
            });
        });
    }
};

// 更新搜索參數方法
const updateSearchParams = (params) => {
    // 更新內部搜索參數
    internalSearchParams.value = { ...params };
    // 不自動觸發查詢，由外部調用 fetchData
};

// 應用主題
const applyTheme = () => {
    // 不需要在每次數據更新時都應用主題
    // 只在主題變更時應用
};

// 監聽主題變更
watch(() => localIsDarkMode.value, (newValue) => {
    // 主題變更時應用主題
    const tableContainer = document.querySelector('.search-results-container');
    if (tableContainer) {
        if (newValue) {
            tableContainer.classList.add('dark-table');
            tableContainer.classList.remove('light-table');
        } else {
            tableContainer.classList.add('light-table');
            tableContainer.classList.remove('dark-table');
        }
    }
});

// 獲取當前頁碼
const getCurrentPage = () => {
    return currentPage.value;
};

// 獲取每頁顯示數量
const getPageSize = () => {
    return pageSize.value;
};

// 獲取排序信息
const getSortInfo = () => {
    return {
        field: sortField.value,
        direction: sortDirection.value
    };
};

// 暴露方法給父組件
defineExpose({
    fetchData,
    updateSearchParams,
    changePage,
    changePageSize,
    applyTheme,
    getCurrentPage,
    getPageSize,
    getSortInfo
});
</script>
<style scoped>
/* 表格樣式 */
.search-results-container {
    min-height: 450px; /* 約10筆資料的高度 */
    position: relative;
    overflow: hidden;
}
</style>
<style>
/* 深色卡片樣式 - 默認 */
.dark-card {
    background-color: transparent !important;
    border-color: rgba(255, 255, 255, 0.8) !important;
}

/* 淺色卡片樣式 */
.light-card {
    background-color: #ffffff !important;
    border-color: #dee2e6 !important;
}

/* 排序圖標樣式 */
.sort-icon {
    font-size: 0.85rem;
    margin-left: 5px;
    display: inline-block;
    vertical-align: middle;
    color: #6c757d; /* 預設時的灰色 */
    opacity: 1; /* 確保預設時就有顏色 */
    font-weight: normal;
}

/* 可排序的表頭樣式 */
.sortable {
    cursor: pointer;
    position: relative;
}

.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 激活的排序欄位標題 - 深色模式 */
.dark-table th[data-sorted="true"] {
    font-weight: bold;
    color: #FFFFFF !important; /* 純白色 */
}

/* 激活的排序圖標 - 深色模式 */
.dark-table th[data-sorted="true"] .sort-icon {
    color: #FFFFFF !important; /* 純白色 */
    font-weight: bold;
}

/* 激活的排序欄位標題 - 淺色模式 */
.light-table th[data-sorted="true"] {
    font-weight: bold;
    color: #0d6efd !important; /* 藍色 */
}

/* 激活的排序圖標 - 淺色模式 */
.light-table th[data-sorted="true"] .sort-icon {
    color: #0d6efd !important; /* 藍色 */
    font-weight: bold;
}

/* 表格樣式 */
.dark-table .table-dark {
    --bs-table-bg: #212529 !important;
    --bs-table-striped-bg: #2c3034 !important;
    --bs-table-striped-color: #fff !important;
    --bs-table-active-bg: #373b3e !important;
    --bs-table-active-color: #fff !important;
    --bs-table-hover-bg: #323539 !important;
    --bs-table-hover-color: #fff !important;
    color: #fff !important;
    border-color: #373b3e !important;
}

.light-table .table-light {
    --bs-table-bg: #ffffff !important;
    --bs-table-striped-bg: #f8f9fa !important;
    --bs-table-striped-color: #212529 !important;
    --bs-table-active-bg: #dee2e6 !important;
    --bs-table-active-color: #212529 !important;
    --bs-table-hover-bg: #e9ecef !important;
    --bs-table-hover-color: #212529 !important;
    color: #212529 !important;
    border-color: #dee2e6 !important;
}

/* 分頁樣式 */
.pagination .page-item.active .page-link {
    color: #fff !important;
    background-color: #007bff !important;
    border-color: #007bff !important;
}

.pagination .page-item.disabled .page-link {
    opacity: 0.65;
}

/* 深色模式下的下拉選單樣式 */
.dark-card .form-select,
.dark-table .form-select {
    background-color: #343a40 !important; /* 黑色背景 */
    color: #fff !important;
    border-color: rgba(255, 255, 255, 0.175) !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 16px 12px !important;
    appearance: none !important;
}

.dark-card .form-select:focus,
.dark-table .form-select:focus {
    border-color: rgba(255, 255, 255, 0.3) !important;
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.1);
}

/* 確保選單選項在深色模式下也有正確的樣式 */
.dark-card .form-select option,
.dark-table .form-select option {
    background-color: #343a40;
    color: #fff;
}

/* 淺色模式下的下拉選單樣式 */
.light-card .form-select,
.light-table .form-select {
    background-color: #fff !important;
    border-color: #ced4da !important;
    color: #212529 !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23212529' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 0.75rem center !important;
    background-size: 16px 12px !important;
    appearance: none !important;
}
</style>
