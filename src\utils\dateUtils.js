/**
 * 日期時間工具函數
 */

/**
 * 獲取當前台灣時間的日期字符串 (YYYY-MM-DD)
 * @returns {string} 格式化的日期字符串
 */
export function getCurrentTaiwanDate() {
  // 獲取當前的 UTC 時間
  const now = new Date();
  
  // 轉換為台灣時間 (UTC+8)
  const taiwanTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
  
  // 格式化為 YYYY-MM-DD
  const year = taiwanTime.getUTCFullYear();
  const month = String(taiwanTime.getUTCMonth() + 1).padStart(2, '0');
  const day = String(taiwanTime.getUTCDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}

/**
 * 將日期轉換為台灣時間的日期字符串 (YYYY-MM-DD)
 * @param {Date|string} date - 要轉換的日期
 * @returns {string} 格式化的日期字符串
 */
export function formatToTaiwanDate(date) {
  if (!date) return '';
  
  // 如果是字符串，先轉換為 Date 對象
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // 獲取台灣時間的年、月、日
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
}

/**
 * 創建一個新的 Date 對象，確保使用台灣時區
 * @param {string|Date} date - 日期字符串或 Date 對象
 * @returns {Date} 新的 Date 對象
 */
export function createTaiwanDate(date) {
  if (!date) {
    // 如果沒有提供日期，返回當前台灣時間
    const now = new Date();
    return new Date(now.getTime() + (8 * 60 * 60 * 1000));
  }
  
  // 如果提供的是字符串
  if (typeof date === 'string') {
    // 如果是 YYYY-MM-DD 格式
    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      const [year, month, day] = date.split('-').map(Number);
      // 創建台灣時間的 Date 對象 (月份需要減 1，因為 Date 構造函數中月份是從 0 開始的)
      return new Date(Date.UTC(year, month - 1, day, 0, 0, 0));
    }
    // 其他格式的字符串，直接創建 Date 對象
    return new Date(date);
  }
  
  // 如果提供的是 Date 對象，返回一個新的 Date 對象
  return new Date(date);
}

// 格式化日期時間為民國年格式
export const formatToRocDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  
  const [datePart, timePart] = dateTimeStr.split(' ');
  const [year, month, day] = datePart.split('-');
  const rocYear = parseInt(year) - 1911;
  
  return `${rocYear} 年 ${month} 月 ${day} 日 ${timePart.substring(0, 5)}`;
};

// 格式化日期時間為民國年格式
export const formatToRocDate = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  
  const [year, month, day] = dateTimeStr.split('-');
  const rocYear = parseInt(year) - 1911;
  
  return `${rocYear} / ${month} / ${day}`;
};

/**
 * 獲取當前台灣時間的日期字符串 (YYYY-MM-DD)
 * @returns {string} 格式化的日期字符串
 */
export function getCurrentTaiwanMonth(date) {
  let taiwanTime;
  if (date) {
    // 如果是字符串，先轉換為 Date 對象
    taiwanTime = typeof date === 'string' ? new Date(date) : date;
  } else {
    // 獲取當前的 UTC 時間
    const now = new Date();
    // 轉換為台灣時間 (UTC+8)
    taiwanTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
  }

  // 格式化為 YYYY-MM-DD
  const year = taiwanTime.getUTCFullYear();
  const month = String(taiwanTime.getUTCMonth() + 1).padStart(2, '0');
  
  return `${year}-${month}`;
}