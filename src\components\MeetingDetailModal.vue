<template>
  <div class="modal-overlay" @click.self="close">
    <div class="modal-box">
      <div class="modal-header">
        <h5 class="modal-title">會議細節</h5>
        <button type="button" class="btn-close" @click="close" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div v-if="loading" class="text-center py-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">載入中...</span>
          </div>
          <p class="mt-2">正在載入會議資料...</p>
        </div>
        <div v-else-if="error" class="alert alert-danger" role="alert">
          {{ error }}
        </div>
        <div v-else class="container-fluid">
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="detail-group">
                <label class="detail-label">會議主題：</label>
                <div class="detail-value">{{ meetingDetail.subject || '不公開' }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="detail-group">
                <label class="detail-label">預約時間：</label>
                <div class="detail-value">{{ formatToRocDate(meetingDetail.reserveDate) }} {{ meetingDetail.reserveDateStart }} ~ {{ meetingDetail.reserveDateEnd }} </div>
              </div>
            </div>
          </div>
          
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="detail-group">
                <label class="detail-label">會&nbsp;&nbsp;議&nbsp;&nbsp;室：</label>
                <div class="detail-value">{{ meetingDetail.roomName || '-' }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="detail-group">
                <label class="detail-label">樓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;層：</label>
                <div class="detail-value">{{ meetingDetail.floor || '-' }}</div>
              </div>
            </div>
          </div>
          
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="detail-group">
                <label class="detail-label">主&nbsp;&nbsp;持&nbsp;&nbsp;人：</label>
                <div class="detail-value">{{ meetingDetail.hostName || '-' }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="detail-group">
                <label class="detail-label">預約單位：</label>
                <div class="detail-value">{{ meetingDetail.departmentName || '-' }}</div>
              </div>
            </div>
          </div>
          
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="detail-group">
                <label class="detail-label">聯&nbsp;&nbsp;絡&nbsp;&nbsp;人：</label>
                <div class="detail-value">{{ meetingDetail.contactName || '-' }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="detail-group">
                <label class="detail-label">聯絡電話：</label>
                <div class="detail-value">{{ meetingDetail.contactPhone || '-' }}</div>
              </div>
            </div>
          </div>
          
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="detail-group">
                <label class="detail-label">預估與會人數：</label>
                <div class="detail-value">{{ meetingDetail.attendees || '-' }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="detail-group">
                <label class="detail-label">會議模式 / 會議情境：</label>
                <div class="detail-value">{{ meetingDetail.meetingMode || '-' }} / {{ meetingDetail.situationMode || '-' }}</div>
              </div>
            </div>
          </div>
          
          <div class="row mb-3">
            
            <div class="col-md-12">
              <div class="detail-group">
                <label class="detail-label">與會單位：</label>
                <div class="detail-value" style="height: 80px;">{{ meetingDetail.departments || '-' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" @click="close">關閉</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, onMounted, watch } from 'vue';
import api from '../utils/api';
import { formatToRocDateTime, formatToRocDate } from '../utils/dateUtils';

const props = defineProps({
  meeting: {
    type: Object,
    required: true
  },
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close']);

// 會議詳情資料
const meetingDetail = ref({});
const loading = ref(false);
const error = ref('');

// 格式化日期時間
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '-';
  
  // 使用 dateUtils 中的 formatToRocDateTime 函數
  return formatToRocDateTime(dateTimeStr);
};

// 關閉模態框
const close = () => {
  emit('close');
};

// 獲取會議詳情
const fetchMeetingDetail = async () => {
  if (!props.meeting || !props.meeting.id) {
    error.value = '無法獲取會議資料：會議ID不存在';
    return;
  }
  
  loading.value = true;
  error.value = '';
  
  try {
    await api.post('/api/record/meeting', { id: props.meeting.id }, {
      callback: function(response) {
        loading.value = false;
        console.log('會議詳情回應:', response.data.result);
        
        if (response.data && response.data.statusCode === '0000') {
          meetingDetail.value = response.data.result || {};
          console.log('獲取會議詳情成功:', meetingDetail.value);
        } else {
          error.value = response.data?.statusMessage || '獲取會議資料失敗';
          console.error('獲取會議詳情失敗:', response.data);
        }
      },
      error: function(err) {
        loading.value = false;
        error.value = '獲取會議資料時發生錯誤';
        console.error('獲取會議詳情時發生錯誤:', err);
      }
    });
  } catch (err) {
    loading.value = false;
    error.value = '獲取會議資料時發生錯誤';
    console.error('獲取會議詳情時發生錯誤:', err);
  }
};

// 監聽 meeting 變化，重新獲取詳情
watch(() => props.meeting, (newMeeting) => {
  if (newMeeting && newMeeting.id) {
    fetchMeetingDetail();
  }
}, { deep: true });

// 監聽 visible 變化，當顯示時獲取詳情
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.meeting && props.meeting.id) {
    fetchMeetingDetail();
  }
});

// 組件掛載時，如果 visible 為 true，則獲取詳情
onMounted(() => {
  if (props.visible && props.meeting && props.meeting.id) {
    fetchMeetingDetail();
  }
});
</script>

<style scoped>
/* 使用與 MeetingModal.vue 一致的樣式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-box {
  background: white;
  color: #333;
  padding: 1rem;
  border-radius: 10px;
  width: 80%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
  animation: modal-appear 0.3s forwards;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 1rem;
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 500;
}

.btn-close {
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
  opacity: 0.5;
  width: 1.5em;
  height: 1.5em;
  padding: 0;
  border: 0;
}

.btn-close:hover {
  opacity: 0.75;
}

.modal-body {
  padding: 0 0.5rem;
}

.modal-footer {
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.detail-group {
  margin-bottom: 1rem;
}

.detail-label {
  font-weight: bold;
  color: #495057;
  margin-bottom: 0.25rem;
  display: block;
}

.detail-value {
  color: #212529;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #dee2e6;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #fff;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
