
.btn-primary {
    background-color: #EADDFF !important; /* 這裡換成你想要的顏色 */
    border-color: #EADDFF !important;
    color: #000 !important; 
}


/* 全版遮罩設定 */
.fullscreen-overlay {
    position: fixed;        /* 固定在螢幕上 */
    top: 0;
    left: 0;
    width: 100vw;           /* 全寬 */
    height: 100vh;          /* 全高 */
    background-color: lightslategray;
    opacity: 75%;           /* 白色 + 30% 透明度 */
    z-index: 1000;          /* 疊加在較高層 */
    display: flex;          /* 使用 flex 布局 */
    justify-content: center; /* 水平居中 */
    align-items: center;     /* 垂直居中 */
}

.loader{
    font-size: 48px;
    color: #FFF;
    display: inline-block;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: 400;
    z-index: 1001;          /* 疊加在較高層 */
    position: relative;
}

.loader:after{
    content: '';
    height: 4px;
    width:0%;
    display: block;
    background: #FF3D00;
    animation: 2s lineGrow linear infinite;
}

@keyframes lineGrow {to{width: 100%;}}