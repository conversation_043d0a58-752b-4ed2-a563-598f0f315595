import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import './style.css'
import 'bootstrap/dist/css/bootstrap.css'
import './assets/css/bootstrap.basic.css';
import './assets/css/layout.css';
import './assets/css/menu.css';
import './assets/css/footer.css';
import './assets/css/pagination.css'; // 添加分頁樣式
import Toast from 'vue-toastification';
import 'vue-toastification/dist/index.css';
// 添加 Font Awesome CSS
import '@fortawesome/fontawesome-free/css/all.min.css';

// 設置全局時區為台灣時區 (UTC+8)
const setTaiwanTimeZone = () => {
  // 檢查瀏覽器是否支持 Intl.DateTimeFormat().resolvedOptions().timeZone
  if (Intl.DateTimeFormat().resolvedOptions().timeZone) {
    console.log('當前時區:', Intl.DateTimeFormat().resolvedOptions().timeZone);
  }
  
  // 為 Date 對象添加一個獲取台灣時間的方法
  Date.prototype.toTaiwanString = function() {
    const taiwanTime = new Date(this.getTime() + (8 * 60 * 60 * 1000));
    return taiwanTime.toISOString();
  };
  
  // 為 Date 對象添加一個獲取台灣日期的方法
  Date.prototype.toTaiwanDateString = function() {
    const taiwanTime = new Date(this.getTime() + (8 * 60 * 60 * 1000));
    return taiwanTime.toISOString().substring(0, 10);
  };
  
  console.log('已設置全局時區為台灣時區 (UTC+8)');
};

// 確保所有樣式表已載入
function ensureStylesLoaded() {
  return new Promise(resolve => {
    if (document.readyState === 'complete') {
      resolve();
    } else {
      window.addEventListener('load', resolve);
    }
  });
}

// 檢查認證狀態
function checkInitialAuth() {
  const token = localStorage.getItem('token');
  const currentPath = window.location.pathname;
  const basePath = import.meta.env.BASE_URL || '/';
  const normalizedPath = currentPath.replace(basePath, '/');
  
  console.log(`初始路徑: ${normalizedPath}, 認證狀態: ${!!token}`);
  
  // 如果當前在需要認證的頁面但沒有令牌，設置重定向標記
  if (normalizedPath !== '/login' && !token) {
    console.log('未認證狀態訪問受保護頁面，設置重定向標記');
    sessionStorage.setItem('redirectToLogin', 'true');
  }
  
  // 如果當前在登入頁面但有令牌，設置重定向標記
  if (normalizedPath === '/login' && token) {
    console.log('已認證狀態訪問登入頁面，設置重定向標記');
    sessionStorage.setItem('redirectToReserve', 'true');
  }
}

// 初始化應用
async function initApp() {
  // 設置台灣時區
  setTaiwanTimeZone();
  
  // 檢查初始認證狀態
  checkInitialAuth();
  
  // 等待所有資源載入完成
  await ensureStylesLoaded();
  
  // 創建並掛載應用
  const app = createApp(App);
  app.use(createPinia());
  app.use(Toast, {
    transition: "Vue-Toastification__bounce",
    maxToasts: 3,
    newestOnTop: true
  });
  app.use(router);
  app.mount('#app');
  
  // 添加全局樣式修復
  document.documentElement.classList.add('app-loaded');
  
  // 處理初始重定向
  if (sessionStorage.getItem('redirectToLogin') === 'true') {
    console.log('執行重定向到登入頁面');
    sessionStorage.removeItem('redirectToLogin');
    router.push('/login');
  } else if (sessionStorage.getItem('redirectToReserve') === 'true') {
    console.log('執行重定向到預約頁面');
    sessionStorage.removeItem('redirectToReserve');
    router.push('/reserve');
  }
}

// 啟動應用
initApp();
