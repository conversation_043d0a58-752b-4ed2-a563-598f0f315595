import axios from 'axios';
import { useSystemLoadingStore } from '../store/SystemLoadingStore';
import { useToast } from 'vue-toastification';
import router from '../router';
const toast = useToast();

const systemLoadingStore = useSystemLoadingStore()

// 創建自定義 axios 實例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_PATH,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 請求攔截器
api.interceptors.request.use(
  config => {
    // 顯示全局載入中狀態
    systemLoadingStore.showDiv();

    console.log('API 請求:', {
      url: config.url,
      method: config.method,
      data: config.data,
      params: config.params
    });

    // 如果有 token，添加到請求頭
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  error => {
    console.error('API 請求錯誤:', error);
    return Promise.reject(error);
  }
);

// 回應攔截器
api.interceptors.response.use(
  response => {
    console.log('API 回應:', {
      status: response.status,
      data: response.data,
      url: response.config.url,
      method: response.config.method
    });

    // 隱藏全局載入中狀態
    setTimeout(() => {
      systemLoadingStore.hideDiv();
      if (response.config.callback) response.config.callback(response);
    }, 200);

    return response;
  },
  error => {
    try {
      if (error.response) {
        if (error.response.status === 401 || error.response.status === 403) {
          localStorage.removeItem('token');
          router.push('/login');
          return;
        } else {
          toast.error("操作失敗！ ", {
            timeout: 2000
          });
        }

        console.error('API 回應錯誤:', {
          status: error.response.status,
          data: error.response.data,
          url: error.config.url,
          method: error.config.method
        });

      } else if (error.request) {
        console.error('API 無回應:', {
          request: error.request,
          url: error.config.url,
          method: error.config.method
        });
      } else {
        console.error('API 請求設置錯誤:', error.message);
      }

    } finally {
      // 隱藏全局載入中狀態
      setTimeout(() => {
        systemLoadingStore.hideDiv();
        if (error.config.callback) error.config.callback(error.response);

      }, 200);
    }



    return Promise.reject(error);
  }
);

// 導出 API 方法
export default api;
