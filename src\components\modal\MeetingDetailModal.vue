<template>
  <div v-if="visible" class="modal-overlay" @click.self="close">
    <div class="modal-box meeting-detail-modal">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h3 class="m-0">會議細節</h3>
        <button class="btn btn-close" @click="close" aria-label="Close"></button>
      </div>
      
      <div class="meeting-detail-content">
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">會議主題：</div>
              <div class="detail-value">{{ meeting.subject || '(無主題)' }}</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">預約時間：</div>
              <div class="detail-value">{{ formatMeetingTime(meeting.startTime, meeting.endTime) }}</div>
            </div>
          </div>
        </div>
        
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">會議室傳備：</div>
              <div class="detail-value">{{ meeting.roomCapacity || '-' }}</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">會議室名稱：</div>
              <div class="detail-value">{{ meeting.roomName || '-' }}</div>
            </div>
          </div>
        </div>
        
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">會議主持人：</div>
              <div class="detail-value">{{ meeting.hostName || '-' }}</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">會議聯絡人：</div>
              <div class="detail-value">{{ meeting.contactName || '-' }}</div>
            </div>
          </div>
        </div>
        
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">會議聯絡人電話：</div>
              <div class="detail-value">{{ meeting.contactPhone || '-' }}</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">會議預約單位：</div>
              <div class="detail-value">{{ meeting.departmentName || '-' }}</div>
            </div>
          </div>
        </div>
        
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">會議與會單位：</div>
              <div class="detail-value">{{ meeting.attendeeDepartment || '-' }}</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">會議模式：</div>
              <div class="detail-value">{{ meeting.meetingMode || '-' }}</div>
            </div>
          </div>
        </div>
        
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">會議情境：</div>
              <div class="detail-value">{{ meeting.meetingType || '-' }}</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="detail-item">
              <div class="detail-label">預估與會人數：</div>
              <div class="detail-value">{{ meeting.attendeeCount || '-' }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="bottom-buttons">
        <button class="btn btn-outline-secondary" @click="close">關閉</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { formatToRocDateTime } from '../../utils/dateUtils'

const props = defineProps({
  visible: Boolean,
  meeting: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'close'])

// 格式化會議時間顯示
const formatMeetingTime = (startTime, endTime) => {
  if (!startTime || !endTime) return '-';
  
  // 使用 dateUtils 中的 formatToRocDateTime 函數
  const startRoc = formatToRocDateTime(startTime);
  const endRoc = formatToRocDateTime(endTime);
  
  // 如果是同一天，只顯示一次日期
  if (startTime.split(' ')[0] === endTime.split(' ')[0]) {
    // 只取結束時間的時間部分
    const endTimePart = endRoc.split(' ').slice(-1)[0];
    return `${startRoc} - ${endTimePart}`;
  } else {
    return `${startRoc} - ${endRoc}`;
  }
};

// 關閉模態框
const close = () => {
  emit('update:visible', false);
  emit('close');
};
</script>

<style scoped>
.meeting-detail-modal {
  max-width: 800px;
  width: 90%;
}

.meeting-detail-content {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.detail-item {
  margin-bottom: 10px;
}

.detail-label {
  font-weight: bold;
  color: #495057;
  margin-bottom: 5px;
}

.detail-value {
  color: #212529;
}

.bottom-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 使用全局樣式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-box {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 20px;
  max-height: 90vh;
  overflow-y: auto;
}
</style>
