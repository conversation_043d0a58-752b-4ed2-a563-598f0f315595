<template>
  <div v-if="visible" class="modal-overlay" @click.self="checkBeforeClose">
    <div class="modal-box">
      <div class="modal-header">
        <h5 class="modal-title">{{ isViewMode ? '會議詳情' : (meetingId ? '編輯會議' : '預約會議') }}</h5>
        <button type="button" class="btn-close" @click="checkBeforeClose" aria-label="Close"></button>
      </div>

      <!-- 預約類型 -->
      <div class="row mt-3 mb-2">
        <div class="col-5">
          <span style="color: red;font-size: 0.75rem;">
            標有<span style="padding: 0 0.25rem">＊</span>的欄位為必要資料，請正確填寫。
          </span>
        </div>
        <div class="col-7 d-flex align-items-center" style="justify-content: space-between;">
          <span class="text-dark">預約類型：</span>
          <div class="form-check">
            <input
              class="form-check-input"
              type="radio"
              name="reserveType"
              id="oneceReserveChange"
              v-model="reserveType"
              value="單次預約"
              checked
            />
            <label class="form-check-label text-dark" for="oneceReserveChange">單次預約</label>
          </div>
          <div class="form-check">
            <input
              class="form-check-input"
              type="radio"
              name="reserveType"
              id="dailyReserveChange"
              v-model="reserveType"
              value="每日預約"
            />
            <label class="form-check-label text-dark" for="dailyReserveChange">每日預約</label>
          </div>
          <div class="form-check">
            <input
              class="form-check-input"
              type="radio"
              name="reserveType"
              id="weeklyReserveChange"
              v-model="reserveType"
              value="每週預約"
            />
            <label class="form-check-label text-dark" for="weeklyReserveChange">每週預約</label>
          </div>
          <div class="form-check">
            <input 
              class="form-check-input"
              type="radio"
              name="reserveType"
              id="monthlyReserveChange"
              v-model="reserveType"
              value="每月預約"
            />
            <label class="form-check-label text-dark" for="monthlyReserveChange">每月預約</label>
          </div>
          <button class="btn btn-danger btn-sm" @click="clearAll">
            全部清除
          </button>
        </div>
      </div>
      
      <div class="row d-flex">
        <!-- 左側：會議基本資訊 -->
        <div class="col-6">
          <div class="card h-100">
            <div class="card-header">
              <h6 class="card-title m-0">會議基本資訊</h6>
            </div>
            <div class="card-body p-3">
              <div class="row mb-3">
                <div class="col-12">
                  <label for="meetingTitle" class="form-label required" id="title">會議主題 </label>
                  <input type="text" class="form-control" v-model="meetingData.subject" placeholder="請輸入會議主題" for="title"
                         :class="{'is-invalid': formErrors.subject}">
                  <div class="invalid-feedback" v-if="formErrors.subject">{{ formErrors.subject }}</div>
                </div>
              </div>
              <div class="row mb-3">
                <div class="col-6">
                  <label for="startDate" class="form-label required">開始日期 </label>
                  <DatePicker 
                    v-model="meetingData.startDate" 
                    :input-class="{'form-control': true, 'is-invalid': formErrors.startDate}"
                    :min-date="today"
                    placeholder="請選擇開始日期"
                  />
                  <div class="invalid-feedback" v-if="formErrors.startDate">{{ formErrors.startDate }}</div>
                </div>
                <div class="col-6">
                  <label for="endDate" class="form-label">結束日期</label>
                  <DatePicker 
                    v-model="meetingData.endDate" 
                    :input-class="{'form-control': true, 'is-invalid': formErrors.endDate}"
                    :min-date="meetingData.startDate || today"
                    placeholder="請選擇結束日期"
                    :disabled="reserveType === '單次預約'"
                  />
                  <div class="invalid-feedback" v-if="formErrors.endDate">{{ formErrors.endDate }}</div>
                </div>
              </div>
              
              <div class="row mb-3">
                <div class="col-6">
                  <label for="startTime" class="form-label required">開始時間 </label>
                  <div class="input-group" :class="{'is-invalid': formErrors.startTime}">
                    <!-- 小時輸入 -->
                    <input
                      type="number"
                      class="form-control text-center pe-0"
                      v-model="formattedStartHour"
                      min="0"
                      max="23"
                    />
                    <!-- "時" 標籤 -->
                    <span class="input-group-text">時</span>

                    <!-- 分鐘輸入 -->
                    <input
                      type="number"
                      class="form-control text-center pe-0"
                      v-model="formattedStartMinute"
                      min="0"
                      max="59"
                      step="15"
                    />
                    <!-- "分" 標籤 -->
                    <span class="input-group-text">分</span>
                  </div>
                  <div class="text-danger small" v-if="formErrors.startTime">{{ formErrors.startTime }}</div>
                </div>
                <div class="col-6">
                  <label for="endTime" class="form-label required">結束時間 </label>
                  <div class="input-group" :class="{'is-invalid': formErrors.endTime}">
                    <!-- 小時輸入 -->
                    <input
                      type="number"
                      class="form-control text-center pe-0"
                      v-model="formattedEndHour"
                      min="0"
                      max="23"
                    />
                    <!-- "時" 標籤 -->
                    <span class="input-group-text">時</span>

                    <!-- 分鐘輸入 -->
                    <input
                      type="number"
                      class="form-control text-center pe-0"
                      v-model="formattedEndMinute"
                      min="0"
                      max="59"
                      step="15"
                    />
                    <!-- "分" 標籤 -->
                    <span class="input-group-text">分</span>
                  </div>
                  <div class="text-danger small" v-if="formErrors.endTime">{{ formErrors.endTime }}</div>
                </div>
              </div>
              
              <div class="row mb-3">
                <div class="col-12">
                  <label for="meetingRoom" class="form-label required">會議室名稱 </label>
                  <select class="form-select" id="meetingRoom" 
                          v-model="meetingData.roomId" 
                          @focus="handleRoomSelectFocus"
                          :class="{'is-invalid': formErrors.roomId}">
                    <option value="">請選擇會議室</option>
                    <template v-for="floor in floors" :key="floor.floorName">
                        <option v-for="room in floor.rooms" :key="room.id" :value="room.id" :data-floor="floor.floor">
                          {{ room.name }} (容納人數：{{ room.capacity }})
                        </option>
                    </template>
                  </select>
                  <div class="invalid-feedback" v-if="formErrors.roomId">{{ formErrors.roomId }}</div>
                </div>
              </div>
              
              <div class="row mb-3">
                <div class="col-12">
                  <label for="floor" class="form-label">樓層</label>
                  <input type="text" class="form-control" id="floor" v-model="selectedFloor" disabled>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右側：會議詳細設定 -->
        <div class="col-6">
          <div class="card h-100">
            <div class="card-header d-flex">
              <h6 class="card-title m-0">會議詳細設定</h6>
              <div class="d-flex align-items-center compact-toggle ms-auto">
                <div class="toggle-switch">
                  <input type="checkbox" id="isPublicToggle" v-model="meetingData.isPublic">
                  <label for="isPublicToggle" class="toggle-label">
                    <span class="toggle-inner"></span>
                  </label>
                </div>
                <span class="ms-2 small" :style="{ color: meetingData.isPublic ? '#28a745' : '#dc3545' }">
                  {{ meetingData.isPublic ? '公開' : '不公開' }}
                </span>
              </div>
            </div>
            <div class="card-body p-3">
              <div class="row">
                <div class="col-6">
                  <label for="hostName" class="form-label required">主持人</label>
                  <input type="input" class="form-control" id="hostName" v-model="meetingData.hostName" :class="{'is-invalid': formErrors.hostName}">
                  <div class="invalid-feedback" v-if="formErrors.hostName">{{ formErrors.hostName }}</div>
                </div>
                <div class="col-6 position-relative">
                  <label for="departmentId" class="form-label required">預約單位 </label>
                  <div class="input-group">
                    <input type="text" class="form-control" id="departmentId" v-model="selectedDepartmentName" 
                           readonly :class="{'is-invalid': formErrors.departmentId}">
                    <button class="btn btn-outline-secondary" type="button" @click="toggleDepartmentModal">
                      <i class="fa fa-search"></i>
                    </button>
                  </div>
                  <div class="text-danger small" v-if="formErrors.departmentId">{{ formErrors.departmentId }}</div>
                  <!-- 預約單位下拉選擇框 -->
                  <div v-if="showDepartmentModal" class="department-dropdown">
                    <div class="card">
                      <div class="card-header"><h6 class="m-0">選擇預約單位</h6></div>
                      <div class="card-body p-3">
                        <div class="mb-3">
                          <label for="companySelect" class="form-label">公司 </label>
                          <select class="form-select" id="companySelect" v-model="selectedCompany" @change="fetchDepartments">
                            <option value="">請選擇</option>
                            <option v-for="company in companyOptions" :key="company.id" :value="company.id">{{ company.cname }}</option>
                          </select>
                        </div>
                        <div class="mb-3">
                          <label for="departmentSelect" class="form-label">單位 </label>
                          <select class="form-select" id="departmentSelect" v-model="selectedDepartment">
                            <option v-for="dept in departmentOptions" :key="dept.id" :value="dept.id">{{ dept.cname }}</option>
                          </select>
                        </div>
                        <div class="text-end">
                          <button class="btn btn-outline-secondary me-2" @click="closeDepartmentModal">取消</button>
                          <button class="btn btn-danger" @click="confirmDepartment">確定</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row mt-3">
                <div class="col-6 position-relative">
                  <label for="contactId" class="form-label required">聯絡人 </label>
                  <div class="input-group mb-3">
                    <input type="input" class="form-control" id="contactId" v-model="meetingData.contactName" readonly>
                    <button class="btn btn-outline-secondary" type="button" @click="toggleContactModal"><i class="fa fa-search"></i></button>
                  </div>
                  <!-- 聯絡人下拉選擇框 -->
                  <div v-if="showContactModal" class="contact-dropdown">
                    <div class="card">
                      <div class="card-header"><h6 class="m-0">快速帶入-聯絡人</h6></div>
                      <div class="card-body p-3">
                        <div class="mb-3">
                          <label for="contactCompanySelect" class="form-label">公司 </label>
                          <select class="form-select" id="contactCompanySelect" v-model="selectedContactCompany">
                            <option value="">請選擇</option>
                            <option v-for="company in companyOptions" :key="company.id" :value="company.id">{{ company.cname }}</option>
                          </select>
                        </div>
                        <div class="mb-3">
                          <label for="contactDepartmentSelect" class="form-label">單位 </label>
                          <select class="form-select" id="contactDepartmentSelect" v-model="selectedContactDepartment">
                            <option value="">請選擇</option>
                            <option v-for="dept in contactDepartmentOptions" :key="dept.id" :value="dept.id">{{ dept.cname }}</option>
                          </select>
                        </div>
                        <div class="mb-3">
                          <label for="contactPersonSelect" class="form-label">人員 </label>
                          <select class="form-select" id="contactPersonSelect" v-model="selectedContactPerson">
                            <option value="">請選擇</option>
                            <option v-for="person in contactPersonOptions" :key="person.id" :value="person.id">{{ person.fullName }}({{ person.jobNum }})</option>
                          </select>
                        </div>
                        <div class="text-end">
                          <button class="btn btn-outline-secondary me-2" @click="closeContactModal">取消</button>
                          <button class="btn btn-danger" @click="confirmContact">確定</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-6">
                  <label for="contactPhone" class="form-label required">聯絡電話</label>
                  <input type="input" class="form-control" id="contactPhone" v-model="meetingData.contactPhone"
                         :class="{'is-invalid': formErrors.contactPhone}">
                  <div class="invalid-feedback" v-if="formErrors.contactPhone">{{ formErrors.contactPhone }}</div>
                </div>
              </div>
            </div>
            <div class="card-body p-3 border-top bg-light">
              <div class="row">
                <div class="col-6">
                  <label for="joinDepartmentId" class="form-label required">與會單位</label>
                  <select class="form-select" id="joinDepartmentId" v-model="meetingData.joinDepartmentId" 
                          @focus="fetchJoinDepartments" @change="handleJoinDepartmentChange"
                          :class="{'is-invalid': formErrors.departments}">
                    <option value="">請選擇</option>
                    <option v-for="dept in joinDepartmentOptions" :key="dept.id" :value="dept.id" 
                            :disabled="meetingData.departments.some(d => d.id === dept.id)">
                      {{ dept.cname }}
                    </option>     
                  </select>
                  <div class="text-danger small" v-if="formErrors.departments">{{ formErrors.departments }}</div>
                </div>
                <div class="col-6">
                  <label for="joinDepartmentIKeyIn" class="form-label">手動輸入與會單位</label>
                  <div class="input-group mb-3" :class="{'is-invalid': formErrors.departments}">
                    <input type="text" class="form-control" id="joinDepartmentIKeyIn" 
                           v-model="manualDepartmentInput" 
                           placeholder="手動輸入與會單位"
                           @keyup.enter="addManualDepartment">
                    <button class="btn btn-outline-secondary" type="button" @click="addManualDepartment">
                      <i class="fa fa-plus"></i>
                    </button>
                  </div>
                </div>
              </div>
              <div class="row d-flex align-items-center justify-content-center">
                <div class="col-12 text-start departments-container">
                  <button type="button" class="btn btn-warning rounded-pill me-2 mb-2" v-for="dept in meetingData.departments" :key="dept.id">
                    {{ dept.cname }} <span class="badge bg-danger ms-2 rounded-pill p-1" @click="removeDepartment(dept)" v-if="!props.isViewMode"><i class="fa fa-close"></i></span>
                  </button>
                </div>
              </div>
            </div>
            <div class="card-body p-3 border-top">
              <div class="row">
                <div class="col-3">
                  <label for="attendees" class="form-label required">出席人數 </label>
                  <input type="number" class="form-control" id="attendees" v-model="meetingData.attendees" 
                         :class="{'is-invalid': formErrors.attendees}">
                  <div class="invalid-feedback" v-if="formErrors.attendees">{{ formErrors.attendees }}</div>
                </div>
                <div class="col-4">
                  <label for="meetingMode" class="form-label required">會議模式 </label>
                  <div :class="{'is-invalid': formErrors.meetingMode}">
                    <div class="form-check">
                      <input type="radio" class="form-check-input" name="mode" id="physical" value="實體會議" v-model="meetingData.meetingMode">
                      <label for="physical" class="form-check-label">實體會議</label>
                    </div>
                    <div class="form-check">
                      <input type="radio" class="form-check-input" name="mode" id="hybrid" value="視訊會議" v-model="meetingData.meetingMode">
                      <label for="hybrid" class="form-check-label">視訊會議</label>
                    </div>
                  </div>
                  <div class="text-danger small" v-if="formErrors.meetingMode">{{ formErrors.meetingMode }}</div>
                </div>
                <div class="col-5">
                  <label for="situationMode" class="form-label required">會議情境 </label>
                  <div :class="{'is-invalid': formErrors.situationMode}">
                    <div class="form-check">
                      <input type="radio" class="form-check-input" name="situation" id="normal" value="一般會議" v-model="meetingData.situationMode">
                      <label for="normal" class="form-check-label">一般會議</label>
                    </div>
                    <div class="form-check">
                      <input type="radio" class="form-check-input" name="situation" id="simple" value="簡報會議" v-model="meetingData.situationMode">
                      <label for="simple" class="form-check-label">簡報會議</label>
                    </div>
                    <div class="form-check">
                      <input type="radio" class="form-check-input" name="situation" id="video" value="視訊會議" v-model="meetingData.situationMode">
                      <label for="video" class="form-check-label">視訊會議</label>
                    </div>
                  </div>
                  <div class="text-danger small" v-if="formErrors.situationMode">{{ formErrors.situationMode }}</div>
                </div>
              </div>
              <!-- 會議連結輸入欄位，僅在選擇實體及視訊會議時顯示，使用單獨的行 -->
              <div class="row mt-3" style="height: 80px;">
                <div v-if="meetingData.meetingMode === '視訊會議'" class="col-12">
                  <label for="meetingLink" class="form-label">會議連結</label>
                  <input 
                    type="text" 
                    id="meetingLink"
                    class="form-control meeting-link-input" 
                    placeholder="請輸入會議連結" 
                    v-model="meetingData.onlineMeetingUrl"
                    :class="{'is-invalid': formErrors.meetingLink}"
                  >
                  <div class="invalid-feedback" v-if="formErrors.meetingLink">{{ formErrors.meetingLink }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部按鈕區域 -->
      <div class="modal-footer">
        <button v-if="!isViewMode" class="btn btn-outline-secondary me-2" @click="checkBeforeClose">取消</button>
        <button v-else class="btn btn-outline-secondary me-2" @click="checkBeforeClose">關閉</button>
        <button v-if="!isViewMode" class="btn btn-danger" @click="confirm">預約</button>
        <button v-if="props.isViewMode && props.meetingDetails && props.meetingDetails.enableEdit" class="btn btn-danger me-2" @click="showDeleteConfirmation">刪除</button>
        <button v-if="props.isViewMode && props.meetingDetails && props.meetingDetails.enableEdit" class="btn btn-success" @click="updateMeeting">異動</button>
      </div>
    </div>
  </div>
  
  <!-- 使用新的確認對話框組件 -->
  <DiscardChangesModal 
    :visible="showDiscardChangesDialog" 
    message="是否捨棄尚未儲存的變更內容"
    @discard="discardChanges" 
    @keep="keepChanges"
    @update:visible="showDiscardChangesDialog = $event"
  />
  
  <!-- 刪除確認對話框 -->
  <DiscardChangesModal 
    :visible="showDeleteConfirmDialog" 
    message="確定要刪除此會議預約嗎？"
    @discard="confirmDelete" 
    @keep="cancelDelete"
    @update:visible="showDeleteConfirmDialog = $event"
  />
</template>
<script setup>
import { ref, reactive, watch, onMounted, onUnmounted, computed } from 'vue'
import { zhTW } from 'date-fns/locale'
import { useToast } from 'vue-toastification';
import api from '../../utils/api'
import { getCurrentTaiwanDate, formatToTaiwanDate, createTaiwanDate } from '../../utils/dateUtils'
import DiscardChangesModal from './DiscardChangesModal.vue'
import DatePicker from '../common/DatePicker.vue'

const toast = useToast();

// 使用 Vue 3 的組合式 API
const props = defineProps({
  // 控制模態框顯示與否
  visible: Boolean,
  floors: Array,
  // 是否允許點擊背景關閉模態框
  closeOnOverlayClick: {
    type: Boolean,
    default: true
  },
  // 預設值
  defaultStartDate: {
    type: String,
    default: ''
  },
  defaultEndDate: {
    type: String,
    default: ''
  },
  defaultStartHour: {
    type: String,
    default: ''
  },
  defaultStartMinute: {
    type: String,
    default: ''
  },
  defaultEndHour: {
    type: String,
    default: ''
  },
  defaultEndMinute: {
    type: String,
    default: ''
  },
  defaultRoomId: {
    type: String,
    default: ''
  },
  // 新增：會議詳情相關 props
  meetingId: {
    type: String,
    default: ''
  },
  meetingDetails: {
    type: Object,
    default: () => ({})
  },
  isViewMode: {
    type: Boolean,
    default: false
  }
})

// 定義事件
const emit = defineEmits(['update:visible', 'confirm', 'cancel', 'refresh-data'])

// 預約類型
const reserveType = ref('單次預約')

// 獲取當前登入用戶信息
const userInfo = computed(() => {
  try {
    const storedInfo = localStorage.getItem('userInfo');
    if (!storedInfo) return {};
    
    const parsedInfo = JSON.parse(storedInfo);
    console.log('獲取到用戶信息:', parsedInfo);
    return parsedInfo;
  } catch (e) {
    console.error('解析用戶信息失敗:', e);
    return {};
  }
});

// 會議資料
const meetingData = reactive({
  subject: '',
  startDate: '',  // 格式為 YYYY-MM-DD
  endDate: '',    // 格式為 YYYY-MM-DD
  startHour: '',  // 格式為 HH
  startMinute: '',// 格式為 mm
  endHour: '',    // 格式為 HH
  endMinute: '',  // 格式為 mm
  roomId: '',
  hostName: '',   // 主持人
  contactId: '',  // 聯絡人ID
  contactName: '', // 聯絡人姓名
  contactPhone: '',// 聯絡電話
  attendees: 0,
  departmentId: '',
  departmentName: '',
  departments: [],
  meetingMode: '實體會議',
  situationMode: '一般會議',
  isPublic: true,
  onlineMeetingUrl: '' // 新增會議連結欄位
})

// 表單驗證狀態
const formErrors = reactive({
  subject: '',
  startDate: '',
  startTime: '',
  endTime: '',
  roomId: '',
  hostName: '',   // 從 host 改為 hostName
  contactName: '',  // 新增聯絡人姓名的錯誤訊息
  contactPhone: '', // 新增聯絡電話的錯誤訊息
  departmentId: '',
  attendees: '',
  meetingMode: '',
  situationMode: '',
  departments: '' // 新增與會單位的錯誤訊息
});

// 獲取今天的日期（台灣時間）
const today = computed(() => {
  return createTaiwanDate(getCurrentTaiwanDate());
});

// 獲取選中會議室的容納人數
const selectedRoomCapacity = computed(() => {
  if (!meetingData.roomId) return 0;
  
  // 遍歷所有樓層和會議室，找到選中的會議室
  for (const floor of props.floors || []) {
    if (floor.rooms) {
      for (const room of floor.rooms) {
        if (room.id === meetingData.roomId) {
          return room.capacity || 0;
        }
      }
    }
  }
  return 0;
});

// 驗證表單
function validateForm() {
  let isValid = true;
  
  // 重置所有錯誤訊息
  Object.keys(formErrors).forEach(key => {
    formErrors[key] = '';
  });
  
  // 驗證會議主題
  if (!meetingData.subject.trim()) {
    formErrors.subject = '請輸入會議主題';
    isValid = false;
  }
  
  // 驗證開始日期
  if (!meetingData.startDate) {
    formErrors.startDate = '請選擇開始日期';
    isValid = false;
  } else {
    // 檢查開始日期是否小於今天
    const startDate = new Date(meetingData.startDate);
    const todayDate = today.value;
    
    // 將日期轉換為 YYYY-MM-DD 格式進行比較
    const formattedStartDate = formatDate(startDate);
    const formattedToday = formatDate(todayDate);
    
    if (formattedStartDate < formattedToday) {
      formErrors.startDate = '開始日期不能小於今天';
      isValid = false;
    }
  }
  
  // 驗證結束日期（如果不是單次預約）
  if (reserveType.value !== '單次預約' && meetingData.endDate) {
    // 檢查結束日期是否小於開始日期
    const startDate = new Date(meetingData.startDate);
    const endDate = new Date(meetingData.endDate);
    
    // 將日期轉換為 YYYY-MM-DD 格式進行比較
    const formattedStartDate = formatDate(startDate);
    const formattedEndDate = formatDate(endDate);
    
    if (formattedEndDate < formattedStartDate) {
      formErrors.startDate = '結束日期不能小於開始日期';
      isValid = false;
    }
  }
  
  // 驗證開始時間
  if (!meetingData.startHour || !meetingData.startMinute) {
    formErrors.startTime = '請設定開始時間';
    isValid = false;
  } else {
    // 檢查開始時間是否小於當前時間（如果是今天）
    const startDate = new Date(meetingData.startDate);
    const todayDate = today.value;
    
    // 將日期轉換為 YYYY-MM-DD 格式進行比較
    const formattedStartDate = formatDate(startDate);
    const formattedToday = formatDate(todayDate);
    
    if (formattedStartDate === formattedToday) {
      // 獲取當前時間
      const now = new Date();
      const currentHour = now.getHours();
      const currentMinute = now.getMinutes();
      
      // 將開始時間轉換為分鐘數
      const startHour = parseInt(meetingData.startHour);
      const startMinute = parseInt(meetingData.startMinute);
      const startTimeInMinutes = startHour * 60 + startMinute;
      
      // 將當前時間轉換為分鐘數
      const currentTimeInMinutes = currentHour * 60 + currentMinute;
      
      // 檢查開始時間是否小於當前時間
      if (startTimeInMinutes < currentTimeInMinutes) {
        formErrors.startTime = '開始時間不能小於當前時間';
        isValid = false;
      }
    }
  }
  
  // 驗證結束時間
  if (!meetingData.endHour || !meetingData.endMinute) {
    formErrors.endTime = '請設定結束時間';
    isValid = false;
  } else {
    // 檢查結束時間是否小於開始時間（如果是同一天）
    if (meetingData.startDate && meetingData.endDate && 
        formatDate(meetingData.startDate) === formatDate(meetingData.endDate)) {
      const startHour = parseInt(meetingData.startHour);
      const startMinute = parseInt(meetingData.startMinute);
      const endHour = parseInt(meetingData.endHour);
      const endMinute = parseInt(meetingData.endMinute);
      
      const startTime = startHour * 60 + startMinute;
      const endTime = endHour * 60 + endMinute;
      
      if (endTime <= startTime) {
        formErrors.endTime = '結束時間必須晚於開始時間';
        isValid = false;
      }
    }
  }
  
  // 其他驗證保持不變
  if (!meetingData.roomId) {
    formErrors.roomId = '請選擇會議室';
    isValid = false;
  }
  
  if (!meetingData.hostName.trim()) {
    formErrors.hostName = '請輸入主持人';
    isValid = false;
  }
  
  if (!meetingData.contactName.trim()) {
    formErrors.contactName = '請選擇聯絡人';
    isValid = false;
  }
  
  if (!meetingData.contactPhone.trim()) {
    formErrors.contactPhone = '請輸入聯絡電話';
    isValid = false;
  }
  
  if (!meetingData.departmentId) {
    formErrors.departmentId = '請選擇預約單位';
    isValid = false;
  }
  
  // 驗證出席人數
  if (!meetingData.attendees || meetingData.attendees <= 0) {
    formErrors.attendees = '請輸入有效的出席人數';
    isValid = false;
  } else if (selectedRoomCapacity.value > 0 && parseInt(meetingData.attendees) > selectedRoomCapacity.value) {
    // 檢查出席人數是否超過會議室容納人數
    formErrors.attendees = `出席人數不能超過會議室容納人數 ${selectedRoomCapacity.value} 人`;
    isValid = false;
  }
  
  if (!meetingData.meetingMode) {
    formErrors.meetingMode = '請選擇會議模式';
    isValid = false;
  }
  
  if (!meetingData.situationMode) {
    formErrors.situationMode = '請選擇會議情境';
    isValid = false;
  }
  
  if (!meetingData.departments || meetingData.departments.length === 0) {
    formErrors.departments = '請至少選擇或輸入一個與會單位';
    isValid = false;
  }
  
  // 如果是實體及視訊會議，驗證會議連結
  if (meetingData.meetingMode === '視訊會議' && !meetingData.onlineMeetingUrl.trim()) {
    formErrors.meetingLink = '請輸入會議連結';
    isValid = false;
  }
  
  return isValid;
}

// 新增：實時檢查開始時間是否小於當前時間
const checkStartTimeValidity = () => {
  // 清除之前的錯誤訊息
  formErrors.startTime = '';
  formErrors.endTime = '';
  
  // 如果沒有設置開始日期或時間，則不進行檢查
  if (!meetingData.startDate) {
    return;
  }
  
  // 檢查開始時間是否為空
  if (!meetingData.startHour || !meetingData.startMinute) {
    formErrors.startTime = '請設定開始時間';
    return;
  }
  
  // 檢查結束時間是否為空
  if (!meetingData.endHour || !meetingData.endMinute) {
    formErrors.endTime = '請設定結束時間';
    return;
  }
  
  // 如果是編輯模式且有會議ID，則不檢查開始時間是否小於當前時間
  if (props.meetingId) {
    return;
  }
  
  const startDate = new Date(meetingData.startDate);
  const todayDate = today.value;
  
  // 將日期轉換為 YYYY-MM-DD 格式進行比較
  const formattedStartDate = formatDate(startDate);
  const formattedToday = formatDate(todayDate);
  
  // 如果開始日期是今天，則檢查時間
  if (formattedStartDate === formattedToday) {
    // 獲取當前時間
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    // 將開始時間轉換為分鐘數
    const startHour = parseInt(meetingData.startHour);
    const startMinute = parseInt(meetingData.startMinute);
    const startTimeInMinutes = startHour * 60 + startMinute;
    
    // 將當前時間轉換為分鐘數
    const currentTimeInMinutes = currentHour * 60 + currentMinute;
    
    // 檢查開始時間是否小於當前時間
    if (startTimeInMinutes < currentTimeInMinutes) {
      formErrors.startTime = '開始時間不能小於當前時間';
    }
  }
  
  // 檢查結束時間是否小於開始時間（如果是同一天）
  if (meetingData.startDate && meetingData.endDate && 
      formatDate(meetingData.startDate) === formatDate(meetingData.endDate)) {
    const startHour = parseInt(meetingData.startHour);
    const startMinute = parseInt(meetingData.startMinute);
    const endHour = parseInt(meetingData.endHour);
    const endMinute = parseInt(meetingData.endMinute);
    
    const startTime = startHour * 60 + startMinute;
    const endTime = endHour * 60 + endMinute;
    
    if (endTime <= startTime) {
      formErrors.endTime = '結束時間必須晚於開始時間';
    }
  }
};

// 監聽開始日期和時間變化，實時檢查有效性
watch([
  () => meetingData.startDate, 
  () => meetingData.startHour, 
  () => meetingData.startMinute,
  () => meetingData.endHour,
  () => meetingData.endMinute
], () => {
  checkStartTimeValidity();
}, { deep: true });

// 填入會議詳情的方法
const fillMeetingDetails = () => {
  const details = props.meetingDetails;
  
  // 填入會議基本資訊
  meetingData.subject = details.subject || '';
  meetingData.hostName = details.hostName || '';
  meetingData.contactName = details.contactName || '';
  meetingData.contactPhone = details.contactPhone || '';
  meetingData.attendees = details.attendees || 0;
  meetingData.departmentId = details.departmentId || '';
  meetingData.meetingMode = details.meetingMode || '實體會議';
  meetingData.situationMode = details.situationMode || '一般會議';
  meetingData.isPublic = details.isPublic !== undefined ? details.isPublic : true;
  meetingData.onlineMeetingUrl = details.onlineMeetingUrl || ''; // 填入會議連結
  meetingData.roomId = details.roomId || ''; // 確保會議室ID被正確設置
  
  // 設置預約類型
  reserveType.value = details.reserveType || '單次預約';
  
  // 設置部門名稱
  selectedDepartmentName.value = details.departmentName || '';
  
  // 處理與會單位資料
  if (details.departments && Array.isArray(details.departments)) {
    meetingData.departments = [...details.departments];
  }
  
  // 根據 enableEdit 決定是否禁用輸入欄位
  setTimeout(() => {
    if (details.enableEdit) {
      enableEditMode();
    } else {
      disableAllInputs();
    }
  }, 100);
  
  // 樓層會自動通過 watch 監聽器根據會議室ID更新
};

// 啟用編輯模式，但預約類型仍不可編輯
const enableEditMode = () => {
  // 啟用大部分輸入欄位
  const inputs = document.querySelectorAll('.modal-box input:not([name="reserveType"]):not(#floor), .modal-box select, .modal-box textarea');
  inputs.forEach(input => {
    input.disabled = false;
  });
  
  // 禁用預約類型選項和樓層輸入框
  const reserveTypeInputs = document.querySelectorAll('.modal-box input[name="reserveType"]');
  reserveTypeInputs.forEach(input => {
    input.disabled = true;
  });
  
  // 確保樓層輸入框始終禁用
  const floorInput = document.querySelector('#floor');
  if (floorInput) {
    floorInput.disabled = true;
  }
  
  // 啟用按鈕
  const buttons = document.querySelectorAll('.modal-box button:not(.btn-close)');
  buttons.forEach(button => {
    button.disabled = false;
  });
  
  // 顯示確認按鈕
  const confirmBtn = document.querySelector('.modal-box .bottom-buttons .btn-danger');
  if (confirmBtn) {
    confirmBtn.style.display = 'block';
  }
};

// 禁用所有輸入欄位（查看模式）
const disableAllInputs = () => {
  // 禁用所有輸入欄位
  const inputs = document.querySelectorAll('.modal-box input, .modal-box select, .modal-box textarea');
  inputs.forEach(input => {
    input.disabled = true;
  });
  
  // 禁用按鈕
  const buttons = document.querySelectorAll('.modal-box button:not(.btn-close)');
  buttons.forEach(button => {
    button.disabled = true;
  });
  
  // 隱藏確認按鈕
  const confirmBtn = document.querySelector('.modal-box .bottom-buttons .btn-danger');
  if (confirmBtn) {
    confirmBtn.style.display = 'none';
  }
};

// 啟用所有輸入欄位的方法
const enableAllInputs = () => {
  const inputs = document.querySelectorAll('.modal-box input:not(.btn), .modal-box select, .modal-box textarea');
  inputs.forEach(input => {
    input.disabled = false;
  });
  
  // 顯示確認按鈕
  const confirmBtn = document.querySelector('.modal-box .btn-danger');
  if (confirmBtn) {
    confirmBtn.style.display = 'block';
  }
  
  // 顯示全部清除按鈕
  const clearBtn = document.querySelector('.modal-box .btn-danger.btn-sm');
  if (clearBtn) {
    clearBtn.style.display = 'block';
  }
};

// 暴露方法給父組件
defineExpose({
  clearAll
});

// 清除所有資料的方法
function clearAll() {
  console.log('清空所有會議資料');
  
  // 重置預約類型
  reserveType.value = '單次預約';
  
  // 重置會議資料
  Object.keys(meetingData).forEach(key => {
    if (Array.isArray(meetingData[key])) {
      meetingData[key] = [];
    } else if (typeof meetingData[key] === 'boolean') {
      meetingData[key] = false;
    } else if (typeof meetingData[key] === 'number') {
      meetingData[key] = 0;
    } else {
      meetingData[key] = '';
    }
  });
  
  // 設置預設值
  meetingData.meetingMode = '實體會議';
  meetingData.situationMode = '一般會議';
  meetingData.isPublic = true;
  meetingData.onlineMeetingUrl = ''; // 清空會議連結
  
  // 清空表單錯誤
  Object.keys(formErrors).forEach(key => {
    formErrors[key] = '';
  });
  
  // 清空其他相關變量
  selectedDepartmentName.value = '';
  selectedDepartment.value = '';
  selectedContactCompany.value = '';
  selectedContactDepartment.value = '';
  selectedContactPerson.value = '';
  
  // 清空樓層
  selectedFloor.value = '';
  
  // 重置表單修改狀態
  formChanged.value = false;
  
  // 清空與會單位列表
  meetingData.departments = [];
  
  // 設置當前用戶為預設聯絡人
  if (userInfo.value && userInfo.value.id) {
    meetingData.contactId = userInfo.value.id;
    meetingData.contactName = userInfo.value.fullName || '';
    meetingData.contactPhone = userInfo.value.phone || '';
    
    // 如果有部門信息，也設置預設部門
    if (userInfo.value.departmentId) {
      meetingData.departmentId = userInfo.value.departmentId;
      selectedDepartmentName.value = userInfo.value.departmentName || '';
      
      // 新增：將登入者的部門設為預設與會單位
      if (userInfo.value.departmentId && userInfo.value.departmentName) {
        meetingData.departments.push({
          id: userInfo.value.departmentId,
          cname: userInfo.value.departmentName
        });
      }
    }
    
    // 不自動設置主持人為當前用戶
    // meetingData.hostName = userInfo.value.fullName || '';
  }
}

/**
 * 關閉模態框
 * 觸發 update:visible 事件更新父組件的綁定值
 */
function close() {
  emit('update:visible', false)
  emit('cancel')
}

/**
 * 確認操作
 * 觸發 confirm 事件並關閉模態框
 */
function confirm() {
  // 表單驗證
  if (!validateForm()) {
    const toast = useToast();
    toast.error('請填寫所有必填欄位', {
      timeout: 3000
    });
    return;
  }
  
  // 格式化日期為 YYYY-MM-DD
  const formattedStartDate = formatDate(meetingData.startDate);
  const formattedEndDate = meetingData.endDate ? formatDate(meetingData.endDate) : formattedStartDate; // 確保結束日期有值
  
  // 格式化時間為 HH:MM
  const formattedStartTime = formatTime(meetingData.startHour, meetingData.startMinute);
  const formattedEndTime = formatTime(meetingData.endHour, meetingData.endMinute);
  
  const meetingDataToSubmit = {
    reserveType: reserveType.value,
    ...meetingData,
    departmentName: selectedDepartmentName.value,
    startDate: formattedStartDate,
    endDate: formattedEndDate,
    startTime: formattedStartTime,
    endTime: formattedEndTime
  };
  
  emit('confirm', meetingDataToSubmit);

  console.log('會議預約確認:', meetingDataToSubmit);

  const toast = useToast();
  api.post('/api/reserve/confirm', meetingDataToSubmit, {
    callback: function(response) {
      console.log(response.data);
      if(response.status === 200) {
        if(response.data && response.data.statusCode !== '0000') {
          toast.error(response.data.statusMessage, {
            timeout: 3000
          });
          return;
        } else {
          // 成功後重置表單修改狀態
          formChanged.value = false;
          
          toast.success('預約成功！', {
            timeout: 2000,
            onClose: () => {
              close();
              // 發出重新查詢的事件
              emit('refresh-data');
            }
          });
        }
      }
    }
  });
}

// 格式化日期為 YYYY-MM-DD
function formatDate(date) {
  if (!date) return '';
  
  // 使用工具函數確保日期使用台灣時區
  return formatToTaiwanDate(date);
}

// 格式化時間為 HH:MM
function formatTime(hour, minute) {
  // 確保小時和分鐘都是數字
  const h = Number(hour);
  const m = Number(minute);
  
  // 使用 padStart 補足兩位數
  const formattedHour = String(h).padStart(2, '0');
  const formattedMinute = String(m).padStart(2, '0');
  
  return `${formattedHour}:${formattedMinute}`;
}

/**
 * 處理背景點擊事件
 * 只有當 closeOnOverlayClick 為 true 時才關閉模態框
 */
function handleOverlayClick() {
  if (props.closeOnOverlayClick) {
    tryClose();
  }
}

const selectedFloor = ref('')

// 監聽會議室選擇變化
watch(() => meetingData.roomId, (newRoomId) => {
  selectedFloor.value = ''
  if (!newRoomId || !props.floors) return
  
  // 尋找選中會議室所在的樓層
  for (const floor of props.floors) {
    const room = floor.rooms.find(room => room.id === newRoomId)
    if (room) {
      selectedFloor.value = `${floor.floorName}`
      break
    }
  }
})

// 預約單位選擇相關
const showDepartmentModal = ref(false)
const selectedCompany = ref('')
const selectedDepartment = ref('')
const departmentOptions = ref([])
const joinDepartmentOptions = ref([])


const companyOptions = ref([])
const selectedDepartmentName = ref('') // 用於顯示部門名稱

// 切換預約單位選擇模態框
function toggleDepartmentModal() {
  showDepartmentModal.value = !showDepartmentModal.value
  api.get('/api/ui/companies', {
    callback:function(response){
      companyOptions.value = response.data
    }
  })
}

function fetchDepartments() {
  if (!selectedCompany.value) return
  api.get(`/api/ui/departments/${selectedCompany.value}`, {
    callback:function(response){
      departmentOptions.value = response.data
    }
  })
} 

// 關閉預約單位選擇模態框
function closeDepartmentModal() {
  showDepartmentModal.value = false
}

// 確認選擇的預約單位
function confirmDepartment() {
  const selectedDeptObj = departmentOptions.value.find(dept => dept.id === selectedDepartment.value)
  if (selectedDeptObj) {
    meetingData.departmentId = selectedDeptObj.id // 存儲ID，後端需要
    selectedDepartmentName.value = selectedDeptObj.cname // 顯示名稱，前端顯示
  }
  closeDepartmentModal()
}

// 聯絡人選擇相關
const showContactModal = ref(false)
const selectedContactCompany = ref('')
const selectedContactDepartment = ref('')
const selectedContactPerson = ref('')
const contactDepartmentOptions = ref([])
const contactPersonOptions = ref([])

// 切換聯絡人選擇框的顯示狀態
function toggleContactModal() {
  showContactModal.value = !showContactModal.value
  // 加載公司列表
  api.get('/api/ui/companies', {
    callback: function(response) {
      console.log(response.data)
      companyOptions.value = response.data
    }
  })
}

// 關閉聯絡人選擇模態框
function closeContactModal() {
  showContactModal.value = false
}

// 確認選擇的聯絡人
function confirmContact() {
  const selectedPerson = contactPersonOptions.value.find(person => person.id === selectedContactPerson.value);
  if (selectedPerson) {
    meetingData.contactId = selectedPerson.id;  // 存儲聯絡人ID
    meetingData.contactName = selectedPerson.fullName;  // 存儲聯絡人姓名
    meetingData.contactPhone = selectedPerson.phone || '';  // 存儲聯絡電話
  }
  closeContactModal();
}

// 獲取與會單位列表
function fetchJoinDepartments() {
  if (joinDepartmentOptions.value.length > 0) return
  api.get('/api/ui/parent/departments', {
    callback: function(response) {
      console.log('與會單位列表:', response.data)
      joinDepartmentOptions.value = response.data
    }
  })
}

// 處理與會單位選擇變化
function handleJoinDepartmentChange() {
  const selectedDeptObj = joinDepartmentOptions.value.find(dept => dept.id === meetingData.joinDepartmentId)
  if (selectedDeptObj) {
    if (meetingData.departments.some(d => d.id === selectedDeptObj.id)) return
    meetingData.departments.push(selectedDeptObj)
  }
}

function removeDepartment(dept) {
  meetingData.departments = meetingData.departments.filter(d => d.id !== dept.id)
}

// 處理點擊外部區域關閉下拉選擇框
function handleClickOutside(event) {
  const departmentDropdown = document.querySelector('.department-dropdown')
  const departmentInput = document.getElementById('departmentId')
  const searchButton = departmentInput?.nextElementSibling
  
  if (showDepartmentModal.value && 
      departmentDropdown && 
      !departmentDropdown.contains(event.target) && 
      event.target !== departmentInput &&
      event.target !== searchButton &&
      !searchButton?.contains(event.target)) {
    showDepartmentModal.value = false
  }

  // 聯絡人下拉框處理
  const contactDropdown = document.querySelector('.contact-dropdown')
  const contactInput = document.getElementById('contactId')
  const contactSearchButton = contactInput?.nextElementSibling
  
  if (showContactModal.value && 
      contactDropdown && 
      !contactDropdown.contains(event.target) && 
      event.target !== contactInput &&
      event.target !== contactSearchButton &&
      !contactSearchButton?.contains(event.target)) {
    showContactModal.value = false
  }
}

// 組件掛載時添加點擊事件監聽器
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 組件卸載時移除點擊事件監聽器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 添加手動輸入與會單位相關的變數
const manualDepartmentInput = ref('');

// 手動添加與會單位
function addManualDepartment() {
  if (!manualDepartmentInput.value.trim()) return;
  
  // 創建一個新的與會單位對象
  const newDept = {
    id: `manual-${Date.now()}`, // 生成唯一ID
    cname: manualDepartmentInput.value.trim(),
    isManual: true // 標記為手動輸入
  };
  
  // 添加到與會單位列表
  meetingData.departments.push(newDept);
  
  // 清空輸入框
  manualDepartmentInput.value = '';
}

// 使用計算屬性確保時間格式為2位數
const formattedStartHour = computed({
  get: () => {
    return meetingData.startHour ? meetingData.startHour.toString().padStart(2, '0') : '00';
  },
  set: (value) => {
    const oldStartHour = meetingData.startHour;
    meetingData.startHour = value ? parseInt(value).toString().padStart(2, '0') : '00';
    
    // 如果開始時間變更，自動調整結束時間
    if (oldStartHour !== meetingData.startHour) {
      updateEndTimeBasedOnStartTime();
    }
  }
});

const formattedStartMinute = computed({
  get: () => {
    return meetingData.startMinute ? meetingData.startMinute.toString().padStart(2, '0') : '00';
  },
  set: (value) => {
    const oldStartMinute = meetingData.startMinute;
    meetingData.startMinute = value ? parseInt(value).toString().padStart(2, '0') : '00';
    
    // 如果開始時間變更，自動調整結束時間
    if (oldStartMinute !== meetingData.startMinute) {
      updateEndTimeBasedOnStartTime();
    }
  }
});

const formattedEndHour = computed({
  get: () => {
    return meetingData.endHour ? meetingData.endHour.toString().padStart(2, '0') : '00';
  },
  set: (value) => {
    meetingData.endHour = value ? parseInt(value).toString().padStart(2, '0') : '00';
  }
});

const formattedEndMinute = computed({
  get: () => {
    return meetingData.endMinute ? meetingData.endMinute.toString().padStart(2, '0') : '00';
  },
  set: (value) => {
    meetingData.endMinute = value ? parseInt(value).toString().padStart(2, '0') : '00';
  }
});

// 根據開始時間更新結束時間（加1小時）
function updateEndTimeBasedOnStartTime() {
  if (!meetingData.startHour || !meetingData.startMinute) return;
  
  // 將開始時間轉換為分鐘
  const startHour = parseInt(meetingData.startHour);
  const startMinute = parseInt(meetingData.startMinute);
  const totalStartMinutes = startHour * 60 + startMinute;
  
  // 加上60分鐘（1小時）
  const totalEndMinutes = totalStartMinutes + 60;
  
  // 計算結束小時和分鐘
  const endHour = Math.floor(totalEndMinutes / 60) % 24; // 使用模運算確保不超過24小時
  const endMinute = totalEndMinutes % 60;
  
  // 更新結束時間
  meetingData.endHour = endHour.toString().padStart(2, '0');
  meetingData.endMinute = endMinute.toString().padStart(2, '0');
}

// 監聽開始時間變化
watch([() => meetingData.startHour, () => meetingData.startMinute], () => {
  updateEndTimeBasedOnStartTime();
}, { immediate: true });

// 設置日期選擇器的最小日期（今天）
const minDate = computed(() => {
  return today.value;
});

// 設置結束日期選擇器的最小日期（開始日期）
const minEndDate = computed(() => {
  return meetingData.startDate || today.value;
});

function deleteMeeting() {
  
} 

/**
 * 更新會議
 * 當會議處於可編輯狀態時，提供更新功能
 */
function updateMeeting() {
  // 表單驗證
  if (!validateForm()) {
    const toast = useToast();
    toast.error('請填寫所有必填欄位', {
      timeout: 3000
    });
    return;
  }
  
  // 格式化日期為 YYYY-MM-DD
  const formattedStartDate = formatDate(meetingData.startDate);
  const formattedEndDate = meetingData.endDate ? formatDate(meetingData.endDate) : formattedStartDate; // 確保結束日期有值
  
  // 格式化時間為 HH:MM
  const formattedStartTime = formatTime(meetingData.startHour, meetingData.startMinute);
  const formattedEndTime = formatTime(meetingData.endHour, meetingData.endMinute);
  
  // 準備更新資料
  const updateData = {
    id: props.meetingDetails.id, // 會議ID，用於識別要更新的會議
    reserveType: reserveType.value,
    ...meetingData,
    departmentName: selectedDepartmentName.value,
    startDate: formattedStartDate,
    endDate: formattedEndDate,
    startTime: formattedStartTime,
    endTime: formattedEndTime
  };
  
  console.log('更新會議資料:', updateData);
  
  const toast = useToast();
  // 呼叫更新API
  api.post('/api/reserve/update', updateData, {
    callback: function(response) {
      console.log(response.data);
      if (response.status === 200) {
        if (response.data && response.data.statusCode !== '0000') {
          toast.error(response.data.statusMessage, {
            timeout: 3000
          });
          return;
        } else {
          // 成功後重置表單修改狀態
          formChanged.value = false;
          
          toast.success('更新成功！', {
            timeout: 2000,
            onClose: () => {
              close();
              // 發出重新查詢的事件
              emit('refresh-data');
            }
          });
        }
      }
    }
  });
}

// 添加變量來追踪表單是否被修改
const formChanged = ref(false);
// 添加變量來控制確認對話框的顯示
const showDiscardChangesDialog = ref(false);
// 添加變量來控制刪除確認對話框的顯示
const showDeleteConfirmDialog = ref(false);

// 監聽表單數據變化
watch(() => meetingData, () => {
  if (props.visible && !props.isViewMode) {
    formChanged.value = true;
  }
}, { deep: true });

// 監聽預約類型變化
watch(() => reserveType.value, () => {
  if (props.visible && !props.isViewMode) {
    formChanged.value = true;
  }
});

// 當模態框打開時重置表單修改狀態
watch(() => props.visible, (isVisible) => {
  if (isVisible) {
    formChanged.value = false;
  }
});

// 檢查是否可以關閉模態框
function checkBeforeClose() {
  // 如果表單被修改過且不是查看模式，顯示確認對話框
  if (formChanged.value && !props.isViewMode) {
    showDiscardChangesDialog.value = true;
  } else {
    // 如果表單未修改或是查看模式，直接關閉
    close();
  }
}

// 處理確認捨棄變更
function discardChanges() {
  // 直接調用原始的 close 函數
  close();
  // 重置表單修改狀態
  formChanged.value = false;
}

// 處理取消捨棄變更
function keepChanges() {
  // 只需關閉確認對話框，不關閉主模態框
  showDiscardChangesDialog.value = false;
}

// 顯示刪除確認對話框
function showDeleteConfirmation() {
  showDeleteConfirmDialog.value = true;
}

// 確認刪除
function confirmDelete() {
  // 這裡應該呼叫刪除API
  const toast = useToast();
  api.post('/api/reserve/delete', { id: props.meetingDetails.id }, {
    callback: function(response) {
      console.log(response.data);
      if (response.status === 200) {
        if (response.data && response.data.statusCode !== '0000') {
          toast.error(response.data.statusMessage, {
            timeout: 3000
          });
          return;
        } else {
          toast.success('刪除成功！', {
            timeout: 2000,
            onClose: () => {
              close();
              emit('refresh-data');
            }
          });
        }
      }
    }
  });
  showDeleteConfirmDialog.value = false;
}

// 取消刪除
function cancelDelete() {
  showDeleteConfirmDialog.value = false;
}

/* -------------------------------------------------
   ＊ NOTE: watch 區塊，監聽資料的變化並執行對應邏輯
   ------------------------------------------------- */

// 【元件事件】會議室選單被打開前觸發事件
function handleRoomSelectFocus() {
  console.log('會議室選單被打開');
  
  // 格式化日期和時間
  const startDate = formatDate(meetingData.startDate);
  const startTime = formatTime(meetingData.startHour, meetingData.startMinute);
  const endTime = formatTime(meetingData.endHour, meetingData.endMinute);
  
  if(!(startDate && startTime && endTime)) {
    return;
  }

  // 準備請求參數
  const params = {
    startDate: startDate,
    startTime: startTime,
    endTime: endTime,
    meetingId: props.meetingId || '' // 如果是編輯模式，需要排除當前會議
  };
  
  console.log('發送請求參數:', params);
  
  // 呼叫 API 檢查會議室可用性

  api.post('/api/reserve/checkAbilityRoom', params, {
    callback: function(response) {
      // 只在控制台輸出結果
      console.log('會議室可用性檢查結果:', response.data);
      
      // 檢查 API 回應狀態
      if (response.data && response.data.statusCode === '0000') {
        // 如果狀態碼為 0000，處理會議室可用性
        if (response.data.result && Array.isArray(response.data.result)) {
          // 獲取會議室選單元素
          const roomSelect = document.getElementById('meetingRoom');
          if (roomSelect) {
            // 遍歷結果，根據 ability 屬性設置會議室是否可選
            response.data.result.forEach(room => {
              console.log(`會議室 ${room.roomName} (${room.roomId}) 可用性: ${room.ability}`);
              
              // 找到對應的選項元素
              const option = Array.from(roomSelect.options).find(opt => opt.value === room.roomId);
              if (option) {
                // 根據 ability 設置選項是否禁用
                option.disabled = !room.ability;
                // 添加視覺提示
                if (!room.ability) {
                  option.style.color = '#999';
                  if(meetingData.roomId === room.roomId) {
                    meetingData.roomId = '';
                  }
                }
              }
            });
          }
        }
      } else {
        // 如果狀態碼不是 0000，輸出錯誤訊息
        console.error('會議室可用性檢查失敗:', response.data?.statusMessage || '未知錯誤');
      }
    },
    error: function(error) {
      console.error('會議室可用性檢查失敗:', error);
    }
  });
}

/* -------------------------------------------------
   ＊ NOTE: watch 區塊，監聽資料的變化並執行對應邏輯
   ------------------------------------------------- */
// 【元件事件】當模態框打開時初始化表單
function initForm() {
  console.log('初始化表單');
  
  // 設置預設值
  meetingData.subject = '';
  meetingData.startDate = createTaiwanDate(props.defaultStartDate || getCurrentTaiwanDate());
  meetingData.endDate = createTaiwanDate(props.defaultEndDate || meetingData.startDate);
  
  // 設置時間
  meetingData.startHour = props.defaultStartHour || '09';
  meetingData.startMinute = props.defaultStartMinute || '00';
  meetingData.endHour = props.defaultEndHour || '10';
  meetingData.endMinute = props.defaultEndMinute || '00';
  
  // 其他欄位初始化...
  meetingData.roomId = props.defaultRoomId || '';
  meetingData.hostName = '';
  meetingData.contactId = '';
  meetingData.contactName = '';
  meetingData.contactPhone = '';
  meetingData.attendees = 0;
  meetingData.departmentId = '';
  meetingData.departmentName = '';
  meetingData.departments = [];
  meetingData.meetingMode = '實體會議';
  meetingData.situationMode = '一般會議';
  meetingData.isPublic = true;
  meetingData.onlineMeetingUrl = '';
  
  // 清除錯誤訊息
  Object.keys(formErrors).forEach(key => {
    formErrors[key] = '';
  });
  
  // 重置表單修改狀態
  formChanged.value = false;
  
  // 設置當前用戶為預設聯絡人
  console.log('設置預設聯絡人，用戶信息:', userInfo.value);
  if (userInfo.value && userInfo.value.id) {
    meetingData.contactId = userInfo.value.id;
    meetingData.contactName = userInfo.value.fullName || '';
    meetingData.contactPhone = userInfo.value.phone || '';
    
    // 如果有部門信息，也設置預設部門
    if (userInfo.value.departmentId) {
      meetingData.departmentId = userInfo.value.departmentId;
      selectedDepartmentName.value = userInfo.value.departmentName || '';
      
      // 將登入者的部門設為預設與會單位
      if (userInfo.value.departmentId && userInfo.value.departmentName) {
        meetingData.departments = [{
          id: userInfo.value.departmentId,
          cname: userInfo.value.departmentName
        }];
      }
    }
  }
}

// 【監聽事件】監聽 visible 變化，當「預約會議 Modal」打開時設置相關預設值
watch(() => props.visible, (isVisible) => {
  if (isVisible) {
    console.log('模態框打開，初始化表單');
    // 初始化表單
    initForm();
    
    // 確保結束日期正確設置
    if (reserveType.value === '單次預約') {
      // 如果是單次預約，結束日期與開始日期相同
      meetingData.endDate = new Date(meetingData.startDate);
    }
    
    // 延遲檢查時間有效性，確保所有值都已設置
    setTimeout(() => {
      checkStartTimeValidity();
      
      // 再次確認聯絡人信息已設置
      if (!meetingData.contactName && userInfo.value && userInfo.value.fullName) {
        console.log('延遲設置聯絡人信息');
        meetingData.contactId = userInfo.value.id;
        meetingData.contactName = userInfo.value.fullName;
        meetingData.contactPhone = userInfo.value.phone || '';
      }
    }, 100);
  }
});

// 監聽 meetingDetails 變化，填入會議詳情
watch(() => props.meetingDetails, (newDetails) => {
  if (props.isViewMode && newDetails && Object.keys(newDetails).length > 0) {
    fillMeetingDetails();
  }
}, { deep: true });


// 監聽聯絡人公司選擇變化，加載對應部門
watch(() => selectedContactCompany.value, (newCompany) => {
  if (!newCompany) {
    contactDepartmentOptions.value = []
    return
  }
  
  api.get(`/api/ui/departments/${newCompany}`, {
    callback: function(response) {
      console.log('聯絡人部門列表:', response.data)
      contactDepartmentOptions.value = response.data
    }
  })
})

// 【監聽事件】監聽聯絡人部門選擇變化，加載對應人員
watch(() => selectedContactDepartment.value, (newDepartment) => {
  if (!newDepartment) {
    contactPersonOptions.value = []
    return
  }
  
  api.get(`/api/ui/users/${newDepartment}`, {
    callback: function(response) {
      console.log('聯絡人列表:', response.data)
      contactPersonOptions.value = response.data
    }
  })
})

// 【監聽事件】監聽預約類型變化
watch(() => reserveType.value, (newType) => {
  console.log('預約類型變更為:', newType);
  
  if (newType === '單次預約') {
    // 如果是單次預約，將結束日期設為與開始日期相同
    if (meetingData.startDate) {
      meetingData.endDate = new Date(meetingData.startDate);
    }
  } else {
    // 如果不是單次預約，確保結束日期可選
    // 如果結束日期未設置或早於開始日期，則設為開始日期
    if (!meetingData.endDate && meetingData.startDate) {
      meetingData.endDate = new Date(meetingData.startDate);
    } else if (meetingData.startDate && meetingData.endDate) {
      // 確保結束日期不早於開始日期
      const startDate = new Date(meetingData.startDate);
      const endDate = new Date(meetingData.endDate);
      
      if (endDate < startDate) {
        meetingData.endDate = new Date(meetingData.startDate);
      }
    }
  }
});

// 【監聽事件】監聽開始日期變化，自動更新結束日期
watch(() => meetingData.startDate, (newStartDate) => {
  if (newStartDate) {
    // 無論預約類型如何，都將結束日期設為與開始日期相同或更晚
    if (!meetingData.endDate || formatDate(meetingData.endDate) < formatDate(newStartDate)) {
      meetingData.endDate = new Date(newStartDate);
    }
  }
});

</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0; 
  left: 0;
  width: 100vw; 
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.modal-box {
  background: white;
  color: #333;
  padding: 1rem;
  padding-top: 0.5rem;
  border-radius: 10px;
  width: 80%;
  max-width: 1280px;
  max-height: 90vh;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
  animation: modal-appear 0.3s forwards;
  position: relative;
}

/* 修正固定頂部樣式 */
.modal-box .d-flex.justify-content-between {
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  padding-top: 1rem;
  padding-bottom: 1.5rem;
  margin-top: -0.5rem;
  margin-left: -1.5rem;
  margin-right: -1.5rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: calc(100% + 3rem);
}

/* 確保 hr 也固定在頂部，並減少間距 */
.modal-box hr {
  position: sticky;
  top: 40px; /* 從50px減少到40px */
  z-index: 9;
  background: white;
  margin-left: -1.5rem;
  margin-right: -1.5rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  width: calc(100% + 3rem);
  margin-top: 0.25rem; /* 添加更小的上邊距 */
  margin-bottom: 0.25rem; /* 添加更小的下邊距 */
}

/* 減少標題和關閉按鈕的間距 */
.modal-box h3 {
  margin-bottom: 0; /* 移除標題的下邊距 */
  font-size: 1.5rem; /* 稍微縮小標題字體 */
}

/* 減少必填欄位提示的間距 */
.modal-box > div > span {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  display: inline-block;
}

/* 與會單位容器樣式 */
.departments-container {
  min-height: 30px; /* 將最小高度從50px減少到30px */
  padding: 5px; /* 減少內邊距 */
  border-radius: 5px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  overflow: visible;
}

/* 自定義滾動條樣式 */
.departments-container::-webkit-scrollbar {
  width: 6px;
}

.departments-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.departments-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

.departments-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 預約單位選擇模態框樣式 */
.department-dropdown {
  position: absolute;
  z-index: 1000;
  width: 100%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-radius: 0 0 5px 5px;
  background-color: white;
  margin-top: -1px;
}

/* 確保預約單位欄位的父容器有相對定位 */
.position-relative {
  position: relative;
}

/* 預約單位下拉選擇框樣式 */
.department-dropdown {
  position: absolute;
  top: calc(100% - 10px); /* 減少間距，只留下2px的空間 */
  left: 0;
  z-index: 1000;
  width: 100%; /* 與父容器同寬 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  background-color: white;
}

/* 確保卡片不會有多餘的邊距 */
.department-dropdown .card {
  margin: 0px;
  border: 2px solid darkblue;
}

/* 確保預約單位欄位的父容器有相對定位 */
.position-relative {
  position: relative;
}

/* 聯絡人下拉選擇框樣式 */
.contact-dropdown {
  position: absolute;
  top: calc(100% - 10px);
  left: 0;
  z-index: 1000;
  width: 100%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  background-color: white;
}

/* 確保卡片不會有多餘的邊距 */
.contact-dropdown .card {
  margin: 0;
  border: 2px solid darkblue;
}

/* 底部按鈕區域樣式 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 18px 20px 2px;
  margin-top: 0; /* 移除上方間距 */
}

.modal-footer button {
  min-width: 80px;
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 500;
}

/* 確保按鈕在小螢幕上也能正確顯示 */
@media (max-width: 480px) {
  .modal-footer {
    padding: 10px 16px 14px;
  }
  
  .modal-footer button {
    min-width: 70px;
    padding: 6px 16px;
  }
}

/* 卡片內容樣式 */
.card-body {
  padding: 1rem;
  flex: 1 1 auto;
}

/* 移除卡片底部多餘的邊框 */
.card {
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
  overflow: hidden; /* 確保內容不會溢出 */
}

/* 確保所有內容都不會溢出容器 */
.row {
  margin-right: 0;
  margin-left: 0;
  width: 100%;
}

/* 確保下拉選單不會導致橫向捲軸 */
.department-dropdown, .contact-dropdown {
  max-width: 100%;
}

/* 確保所有卡片不會溢出 */
.card {
  max-width: 100%;
}

/* 必填欄位標籤樣式 */
.form-label.required::after {
  content: " *";
  color: red;
}

/* 錯誤訊息樣式 */
.invalid-feedback, .text-danger.small {
  display: block;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* 錯誤輸入框樣式 */
.is-invalid {
  border-color: #dc3545 !important;
}

/* 確保日期選擇器在錯誤時也有紅色邊框 */
.is-invalid .mx-input {
  border-color: #dc3545 !important;
}

/* 修復所有單選按鈕樣式 */
.form-check {
  display: inline-flex;
  align-items: center;
  margin-right: 15px;
  margin-bottom: 0;
  position: relative;
}

.form-check-input {
  position: relative;
  margin-top: 0;
  margin-right: 5px;
  flex-shrink: 0;
  width: 1em;
  height: 1em;
}

.form-check-input[type="radio"] {
  border-radius: 50%;
}

.form-check-label {
  margin-bottom: 0;
  font-size: 0.9rem;
  white-space: nowrap;
  user-select: none;
}

/* 修復開關式單選按鈕樣式 */
.form-switch {
  padding-left: 2.5em;
}

.form-switch .form-check-input {
  width: 2em;
  margin-left: -2.5em;
}

/* 修復預約類型區域 */
.reservation-type-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 5px;
}

/* 修復會議模式和會議情境區域 */
.form-check-inline {
  display: inline-flex;
  align-items: center;
  padding-left: 0;
  margin-right: 1rem;
}

/* 修復是否公開區域 */
.form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
  margin-right: 0.3125rem;
  margin-left: 0;
}

/* 修復開關式按鈕 */
.toggle-button {
  display: inline-flex;
  align-items: center;
  background-color: #0d6efd;
  color: white;
  border-radius: 30px;
  padding: 2px;
  position: relative;
  cursor: pointer;
  width: 60px;
  height: 30px;
}

.toggle-button-circle {
  width: 26px;
  height: 26px;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  transition: transform 0.2s;
}

.toggle-button-circle.active {
  transform: translateX(30px);
}

.toggle-button-text {
  font-size: 0.75rem;
  position: absolute;
  width: 100%;
  text-align: center;
  user-select: none;
}

/* 切換開關樣式 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-label {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  background-color: #ccc;
  border-radius: 34px;
  cursor: pointer;
  transition: .4s;
  margin-bottom: 0;
}

.toggle-inner {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: .4s;
}

input:checked + .toggle-label {
  background-color: #28a745;
}

input:checked + .toggle-label .toggle-inner {
  transform: translateX(26px);
}

.toggle-switch-label {
  position: absolute;
  left: 60px;
  top: 2px;
  white-space: nowrap;
  font-size: 0.8rem;
  color: #666;
}

/* 卡片標題樣式 */
.card-header {
  background-color: rgba(0, 0, 0, 0.03);
  padding: 0.5rem 1.25rem; /* 減少上下內邊距 */
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  height: 48px; /* 減少卡片標題高度 */
  display: flex;
  align-items: center;
}

/* 緊湊型切換開關容器 */
.compact-toggle {
  height: 28px; /* 固定高度 */
}

/* 切換開關樣式 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px; /* 減少寬度 */
  height: 20px; /* 減少高度 */
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-label {
  position: relative;
  display: inline-block;
  width: 40px; /* 減少寬度 */
  height: 20px; /* 減少高度 */
  background-color: #dc3545; /* 紅色背景表示不公開 */
  border-radius: 34px;
  cursor: pointer;
  transition: .4s;
  margin-bottom: 0;
}

.toggle-inner {
  position: absolute;
  content: "";
  height: 16px; /* 減少高度 */
  width: 16px; /* 減少寬度 */
  left: 2px;
  bottom: 2px;
  background-color: white;
  border-radius: 50%;
  transition: .4s;
}

input:checked + .toggle-label {
  background-color: #28a745; /* 綠色背景表示公開 */
}

input:checked + .toggle-label .toggle-inner {
  transform: translateX(20px); /* 調整移動距離 */
}

/* 確保標題中的文字垂直居中且字體更小 */
.card-header .small {
  line-height: 1;
  font-size: 0.75rem; /* 減少字體大小 */
}

/* 會議模式和情境選項樣式 */
.form-check {
  margin-bottom: 8px;
}

.form-check-input {
  margin-top: 0.25rem;
}

.form-check-label {
  margin-left: 4px;
}

/* 會議連結輸入欄位樣式 */
.meeting-link-input {
  width: 100%;
  padding: 8px 12px;
  font-size: 0.9rem;
  border-radius: 4px;
  border: 1px solid #ced4da;
}

.meeting-link-input:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 錯誤狀態樣式 */
.meeting-link-input.is-invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.meeting-link-input.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}
</style>
