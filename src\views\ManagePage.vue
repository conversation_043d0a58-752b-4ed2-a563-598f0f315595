<template>
    <div>
        <MainLayout>
            <template #title>
                <span class="text-light fs-3 fw-bold">會議室管理</span>
            </template>
            <template #subTitle>
                <span class="text-white-50">搜尋現有會議室資訊</span>
            </template>
            <template #content>
                <div class="position-relative">
                    <!-- 絕對定位的按鈕 -->
                    <div class="position-absolute top-0 end-0" style="top: -110px !important; right: 0px !important;">
                        <button class="btn btn-success rounded-pill" @click="addRoom">
                            <i class="fa fa-plus"></i>
                            新增會議室
                        </button>
                    </div>
                    
                    <!-- 原有的內容 -->
                    <div class="card bg-transparent border-light rounded-4 p-4 mb-4">
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-light mb-3">搜尋現有會議室資訊</h5>
                                
                                <!-- 查詢條件區域 -->
                                <div class="row g-3 align-items-center">
                                    <!-- 港別 -->
                                    <div class="col-md-2">
                                        <label for="portSelect" class="form-label text-light">港別</label>
                                        <select id="portSelect" class="form-select" v-model="searchParams.portId">
                                            <option value="">請選擇</option>
                                            <option v-for="port in portOptions" :key="port.portId" :value="port.portId">
                                                {{ port.portName }}
                                            </option>
                                        </select>
                                    </div>
                                    
                                    <!-- 辦公室別 -->
                                    <div class="col-md-3">
                                        <label for="officeSelect" class="form-label text-light">辦公室別</label>
                                        <select id="officeSelect" class="form-select" v-model="searchParams.officeId">
                                            <option value="">請選擇</option>
                                            <option v-for="office in officeOptions" :key="office.officeId" :value="office.officeId">
                                                {{ office.officeName }}
                                            </option>
                                        </select>
                                    </div>
                                    
                                    <!-- 樓層 -->
                                    <div class="col-md-2">
                                        <label for="floorSelect" class="form-label text-light">樓層</label>
                                        <select id="floorSelect" class="form-select" v-model="searchParams.floor">
                                            <option value="">請選擇</option>
                                            <option v-for="floor in floorOptions" :key="floor.floor" :value="floor.floor">
                                                {{ floor.floorName }}
                                            </option>
                                        </select>
                                    </div>
                                    
                                    <!-- 會議室名稱 -->
                                    <div class="col-md-3">
                                        <label for="roomSelect" class="form-label text-light">會議室名稱</label>
                                        <select id="roomSelect" class="form-select" v-model="searchParams.roomId">
                                            <option value="">請選擇</option>
                                            <option v-for="room in roomOptions" :key="room.roomId" :value="room.roomId">
                                                {{ room.roomName }}
                                            </option>
                                        </select>
                                    </div>
                                    
                                    <!-- 清除按鈕 -->
                                    <button class="btn btn-outline-light rounded-pill col-md-1 mt-5" @click="clearSearch">清除</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card bg-transparent border-light rounded-4 p-4 results-card" :class="{'light-card': !isDarkMode, 'dark-card': isDarkMode}">
                        <div class="row">        
                            <!-- 搜尋結果區域 -->
                            <div class="col-12">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <h5 class="result-title" :class="{'text-light': isDarkMode, 'text-dark': !isDarkMode}">搜尋結果</h5>
                                    <div class="theme-toggle d-flex align-items-center">
                                        <span class="me-2 small" :class="{'text-light': isDarkMode, 'text-dark': !isDarkMode}">{{ isDarkMode ? '深色版' : '淺色版' }}</span>
                                        <div class="form-check form-switch mb-0">
                                            <input class="form-check-input" type="checkbox" id="themeSwitch" v-model="isDarkMode" @change="toggleTheme">
                                        </div>
                                    </div>
                                </div>
                                <div class="table-responsive search-results-container" :class="{'light-table': !isDarkMode, 'dark-table': isDarkMode}">
                                    <table class="table table-dark table-hover">
                                        <thead>
                                            <tr>
                                                <th class="text-secondary" @click="sortTable('port_id')">
                                                    港別
                                                    <span class="sort-icon">{{ getSortIcon('port_id') }}</span>
                                                </th>
                                                <th class="text-secondary" @click="sortTable('office_id')">
                                                    辦公室別
                                                    <span class="sort-icon">{{ getSortIcon('office_id') }}</span>
                                                </th>
                                                <th class="text-secondary" @click="sortTable('floor')">
                                                    樓層
                                                    <span class="sort-icon">{{ getSortIcon('floor') }}</span>
                                                </th>
                                                <th class="text-secondary" @click="sortTable('room_name')">
                                                    會議室名稱
                                                    <span class="sort-icon">{{ getSortIcon('room_name') }}</span>
                                                </th>
                                                <th class="text-secondary" @click="sortTable('is_enabled')">
                                                    啟用狀態
                                                    <span class="sort-icon">{{ getSortIcon('is_enabled') }}</span>
                                                </th>
                                                <th class="text-secondary" @click="sortTable('online_meeting')">
                                                    視訊設備
                                                    <span class="sort-icon">{{ getSortIcon('online_meeting') }}</span>
                                                </th>
                                                <th class="text-secondary" @click="sortTable('env_device')">
                                                    環控設備
                                                    <span class="sort-icon">{{ getSortIcon('env_device') }}</span>    
                                                </th>
                                                <th class="text-secondary text-center">功能</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="room in searchResults" :key="room.id">
                                                <td>{{ room.portName }}</td>
                                                <td>{{ room.officeName }}</td>
                                                <td>{{ room.floorName }}</td>
                                                <td>{{ room.roomName }}</td>
                                                <td>{{ room.isEnabled ? '啟用' : '停用' }}</td>
                                                <td>{{ room.onlineMeeting? '有' : '無' }}</td>
                                                <td>{{ room.envDevice? '有' : '無' }}</td>
                                                <td>
                                                    <div class="d-flex justify-content-center gap-1">
                                                        <button class="btn btn-action" title="編輯會議室" @click="editRoom(room)">
                                                            <i class="fa fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-action" title="刪除會議室" @click="confirmDeleteRoom(room.id)">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>    
                    </div>
                    <!-- 在搜尋結果表格下方添加分頁條 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <!-- 每頁筆數選單 -->
                                <div class="d-flex align-items-center">
                                    <span class="text-light me-2 small">每頁顯示：</span>
                                    <select class="form-select form-select-sm page-size-select" v-model="pageSize" @change="changePageSize">
                                        <option value="10">10</option>
                                        <option value="20">20</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>
                                
                                <!-- 分頁條 -->
                                <nav aria-label="會議記錄分頁">
                                    <ul class="pagination pagination-sm mb-0">
                                        <!-- 首頁按鈕 -->
                                        <li class="page-item" :class="{disabled: currentPage === 1}">
                                            <a class="page-link" href="#" @click.prevent="goToFirstPage" :aria-disabled="currentPage === 1">
                                                <span>&laquo;</span>
                                            </a>
                                        </li>
                                        <!-- 上一頁按鈕 -->
                                        <li class="page-item" :class="{disabled: currentPage === 1}">
                                            <a class="page-link" href="#" @click.prevent="goToPrevPage" :aria-disabled="currentPage === 1">
                                                <span>&lsaquo;</span>
                                            </a>
                                        </li>
                                        <!-- 頁碼按鈕 -->
                                        <li v-for="page in pageNumbers" :key="page" class="page-item" :class="{active: page === currentPage}">
                                            <a class="page-link" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                                        </li>
                                        <!-- 下一頁按鈕 -->
                                        <li class="page-item" :class="{disabled: currentPage === totalPages}">
                                            <a class="page-link" href="#" @click.prevent="goToNextPage" :aria-disabled="currentPage === totalPages">
                                                <span>&rsaquo;</span>
                                            </a>
                                        </li>
                                        <!-- 尾頁按鈕 -->
                                        <li class="page-item" :class="{disabled: currentPage === totalPages}">
                                            <a class="page-link" href="#" @click.prevent="goToLastPage" :aria-disabled="currentPage === totalPages">
                                                <span>&raquo;</span>
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                                
                                <!-- 右側空白區域，保持對稱 -->
                                <div style="width: 150px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </MainLayout>
        <RoomEditModal
            :visible="showEditModal"
            :room="selectedRoom"
            :portOptions="portsData"
            @update:visible="showEditModal = $event"
            @save="saveRoom"
            @delete="confirmDeleteRoom"
            @close="closeEditModal"
        />
        
        <!-- 添加刪除確認模態框 -->
        <DiscardChangesModal 
            :visible="showDeleteConfirmDialog" 
            message="確定刪除此會議室？<br>此操作將無法復原。"
            @discard="executeDelete" 
            @keep="cancelDelete"
            @update:visible="showDeleteConfirmDialog = $event"
        />
    </div>
</template>

<script setup>
import MainLayout from '../components/MainLayout.vue';
import RoomEditModal from '../components/modal/RoomEditModal.vue';
import DiscardChangesModal from '../components/modal/DiscardChangesModal.vue';
import { ref, reactive, onMounted, computed, watch, nextTick, onBeforeMount } from 'vue';
import api from '../utils/api';
import { useToast } from 'vue-toastification';

const toast = useToast();

// 查詢參數
const searchParams = reactive({
    portId: '', officeId: '', floor: '', roomId: '',
    sorts: [{ field: 'room_name', direction: 'asc' }],
    page: 1, limit: 10
});

// 下拉選單選項
const portOptions = ref([]);
const officeOptions = ref([]);
const floorOptions = ref([]);
const roomOptions = ref([]);

// 原始API回應資料
const portsData = ref([]);

// 搜尋結果與狀態管理
const searchResults = ref([]);
const hasSearched = ref(false);
const isDarkMode = ref(true); // 預設為深色模式

// 分頁相關狀態
const pageSize = ref(10);
const currentPage = ref(1);
const totalItems = ref(0);
const totalPages = ref(0);

// 排序相關狀態
const sortField = ref('room_name');
const sortDirection = ref('asc');

// 計算要顯示的頁碼
const pageNumbers = computed(() => {
    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage.value - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;
    
    if (endPage > totalPages.value) {
        endPage = totalPages.value;
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
    }
    
    return pages;
});

// 分頁控制函數
const changePageSize = () => {
    currentPage.value = 1;
    searchRooms();
};

const changePage = (page) => {
    if (page < 1 || page > totalPages.value || page === currentPage.value) return;
    currentPage.value = page;
    searchRooms();
};

const goToFirstPage = () => currentPage.value > 1 && changePage(1);
const goToPrevPage = () => currentPage.value > 1 && changePage(currentPage.value - 1);
const goToNextPage = () => currentPage.value < totalPages.value && changePage(currentPage.value + 1);
const goToLastPage = () => currentPage.value < totalPages.value && changePage(totalPages.value);

// 初始化頁面
onBeforeMount(() => {
    loadOptions();
    searchRooms();
    initTheme();
});

onMounted(() => {
    applyInitialSortStyle();
});

// 主題相關函數
const initTheme = () => {
    const savedTheme = localStorage.getItem('tableTheme');
    if (savedTheme !== null) {
        isDarkMode.value = savedTheme === 'true';
    }
    applyTheme();
};

const toggleTheme = () => {
    localStorage.setItem('tableTheme', isDarkMode.value);
    applyTheme();
};

const applyTheme = () => {
    const tableContainer = document.querySelector('.search-results-container');
    if (tableContainer) {
        if (isDarkMode.value) {
            tableContainer.classList.add('dark-table');
            tableContainer.classList.remove('light-table');
        } else {
            tableContainer.classList.add('light-table');
            tableContainer.classList.remove('dark-table');
        }
    }
};

// 排序相關函數
const sortTable = (field) => {
    // 處理排序方向
    if (field === sortField.value) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    } else {
        sortField.value = field;
        sortDirection.value = 'asc';
    }
    
    // 更新排序參數
    const fieldMapping = {
        'port_id': 'port_id', 'office_id': 'office_id',
        'floor': 'floor', 'room_name': 'room_name',
        'is_enabled': 'is_enabled', 'online_meeting': 'online_meeting',
        'env_device': 'env_device',
    };
    
    searchParams.sorts = [{
        field: fieldMapping[field] || field,
        direction: sortDirection.value
    }];
    
    // 重新查詢資料
    searchRooms();
    
    // 更新表頭樣式
    updateSortHeaderStyle(field);
};

const updateSortHeaderStyle = (field) => {
    nextTick(() => {
        const headers = document.querySelectorAll('th');
        headers.forEach(header => header.removeAttribute('data-sorted'));
        
        const fieldMap = {
            'port_id': 0, 'office_id': 1, 'floor': 2, 'room_name': 3,
            'is_enabled': 4, 'online_meeting': 5, 'env_device': 6
        };
        
        if (fieldMap[field] !== undefined) {
            const headerIndex = fieldMap[field];
            const header = headers[headerIndex];
            if (header) {
                header.setAttribute('data-sorted', 'true');
            }
        }
    });
};

const getSortIcon = (field) => {
    if (field !== sortField.value) return '⇅';
    return sortDirection.value === 'asc' ? '↑' : '↓';
};

// 載入選項資料
const loadOptions = async () => {
    return new Promise((resolve, reject) => {
        try {
            api.get('/api/ui/port/rooms', {
                callback: function(response) {
                    // 保存原始資料
                    portsData.value = response.data;
                    // 設置港別選項
                    portOptions.value = response.data;
                    console.log('港別選項已更新:', portOptions.value);
                    
                    // 如果有選擇港別，則更新辦公室選項
                    if (searchParams.portId) {
                        watchPortChange();
                    }
                    
                    // 如果有選擇辦公室，則更新樓層選項
                    if (searchParams.officeId) {
                        watchOfficeChange();
                    }
                    
                    // 如果有選擇樓層，則更新會議室選項
                    if (searchParams.floor) {
                        watchFloorChange();
                    }
                    
                    resolve();
                }
            });
        } catch (error) {
            console.error('載入選項時發生錯誤:', error);
            reject(error);
        }
    });
};

// 監聽港別變更，更新辦公室選項
const watchPortChange = () => {
    // 清空相關選項
    searchParams.officeId = '';
    searchParams.floor = '';
    searchParams.roomId = '';
    officeOptions.value = [];
    floorOptions.value = [];
    roomOptions.value = [];
    
    if (!searchParams.portId) return;
    
    // 從保存的原始資料中找到對應的港別資料
    const selectedPort = portsData.value.find(port => port.portId === searchParams.portId);
    if (selectedPort && selectedPort.offices) {
        // 設置辦公室選項
        officeOptions.value = selectedPort.offices ;
        
        console.log('辦公室選項:', officeOptions.value);
    }
};

// 監聽辦公室變更，更新樓層選項
const watchOfficeChange = () => {
    // 清空相關選項
    searchParams.floor = '';
    searchParams.roomId = '';
    floorOptions.value = [];
    roomOptions.value = [];
    
    if (!searchParams.portId || !searchParams.officeId) return;
    
    // 從保存的原始資料中找到對應的辦公室資料
    const selectedPort = portsData.value.find(port => port.portId === searchParams.portId);
    if (selectedPort && selectedPort.offices) {
        const selectedOffice = selectedPort.offices.find(office => office.officeId === searchParams.officeId);
        if (selectedOffice && selectedOffice.floors) {
            // 設置樓層選項
            floorOptions.value = selectedOffice.floors ;
            
            console.log('樓層選項:', floorOptions.value);
        }
    }
};

// 監聽樓層變更，更新會議室選項
const watchFloorChange = () => {
    // 清空相關選項
    searchParams.roomId = '';
    roomOptions.value = [];
    
    if (!searchParams.portId || !searchParams.officeId || !searchParams.floor) return;
    
    // 從保存的原始資料中找到對應的樓層資料
    const selectedPort = portsData.value.find(port => port.portId === searchParams.portId);
    if (selectedPort && selectedPort.offices) {
        const selectedOffice = selectedPort.offices.find(office => office.officeId === searchParams.officeId);
        if (selectedOffice && selectedOffice.floors) {
            const selectedFloor = selectedOffice.floors.find(floor => floor.floor === searchParams.floor);
            if (selectedFloor && selectedFloor.rooms) {
                // 設置會議室選項
                roomOptions.value = selectedFloor.rooms ;
                
                console.log('會議室選項:', roomOptions.value);
            }
        }
    }
};

// 搜尋會議室
const searchRooms = async () => {
    try {
        const params = {
            ...searchParams,
            page: currentPage.value,
            limit: pageSize.value
        };
        
        await api.post('/api/manage/search', params, {
            callback: function(response) {
                if (response.data?.data?.data) {
                    searchResults.value = response.data.data.data || [];
                    totalItems.value = response.data.data.total || 0;
                    totalPages.value = response.data.data.totalPage || 0;
                    hasSearched.value = true;
                    setTimeout(applyTheme, 0);
                } else {
                    searchResults.value = [];
                    totalItems.value = 0;
                    totalPages.value = 0;
                }
            }
        });
    } catch (error) {
        console.error('搜尋會議室失敗:', error);
        searchResults.value = [];
        totalItems.value = 0;
        totalPages.value = 0;
    }
};

// 應用初始排序樣式
const applyInitialSortStyle = () => {
    const headers = document.querySelectorAll('th');
    headers.forEach(header => header.removeAttribute('data-sorted'));
    
    const fieldMap = {
        'port_id': 0, 'office_id': 1, 'floor': 2, 'room_name': 3, 'is_enabled': 4, 'online_meeting': 5, 'env_device': 6
    };
    
    if (fieldMap[sortField.value] !== undefined) {
        const headerIndex = fieldMap[sortField.value];
        const header = headers[headerIndex];
        if (header) {
            header.setAttribute('data-sorted', 'true');
        }
    }
};

// 清除搜尋條件
const clearSearch = () => {
    searchParams.portId = '';
    searchParams.officeId = '';
    searchParams.floor = '';
    searchParams.roomId = '';
    searchParams.sorts = [{field: 'room_name', direction: 'asc'}];
    sortField.value = 'room_name';
    sortDirection.value = 'asc';
    currentPage.value = 1;
    
    // 清空選項
    officeOptions.value = [];
    floorOptions.value = [];
    roomOptions.value = [];
    
    searchRooms();

    nextTick(() => {
        const headers = document.querySelectorAll('th');
        headers.forEach(header => header.removeAttribute('data-sorted'));
        applyInitialSortStyle();
    });
};

const addRoom = () => {
    // 初始化一個新的會議室物件，清除所有原先資料
    selectedRoom.value = {
        id: '',
        portId: '',
        officeId: '',
        floor: '',
        roomName: '',
        capacity: 0,
        onlineMeeting: false,
        envDevice: false,
        isEnabled: true
    };
    
    // 打開編輯模態視窗
    showEditModal.value = true;
};

// 編輯與刪除功能
const editRoom = async (room) => {
    console.log('編輯會議室:', room);
    
    // 確保選項數據已載入
    if (portsData.value.length === 0) {
        await loadOptions();
    }
    
    // 將API返回的資料格式轉換為模態視窗需要的格式
    selectedRoom.value = {
        id: room.id,
        portId: room.portId,
        officeId: room.officeId,
        floor: room.floor,
        roomName: room.roomName,
        capacity: room.capacity || 0,
        onlineMeeting: room.onlineMeeting || false,
        envDevice: room.envDevice || false,
        isEnabled: room.isEnabled !== undefined ? room.isEnabled : true
    };
    
    // 打開編輯模態視窗
    showEditModal.value = true;
};

// 添加刪除確認模態框相關狀態
const showDeleteConfirmDialog = ref(false);
const roomIdToDelete = ref(null);

// 確認刪除會議室 - 修改為顯示確認對話框
const confirmDeleteRoom = (roomId) => {
    roomIdToDelete.value = roomId;
    showDeleteConfirmDialog.value = true;
};

// 執行刪除操作
const executeDelete = () => {
    if (!roomIdToDelete.value) return;
    
    console.log('確認刪除會議室:', roomIdToDelete.value);
    
    // 記錄當前選中的會議室ID
    const deletedRoomId = roomIdToDelete.value;
    
    // 呼叫刪除 API
    api.post('/api/manage/delete', { id: deletedRoomId }, {
        callback: async function(response) {
            if (response.data && response.data.statusCode === '0000') {
                toast.success('會議室已刪除', {
                    timeout: 2000
                });
                
                // 先重新載入選項資料
                await loadOptions();
                
                // 如果刪除的是當前選中的會議室，則重置會議室選擇
                if (searchParams.roomId === deletedRoomId) {
                    searchParams.roomId = '';
                }
                
                // 重新搜尋會議室列表
                searchRooms();
            } else {
                toast.error('刪除失敗: ' + (response.data?.statusMessage || '未知錯誤'), {
                    timeout: 3000
                });
            }
        }
    });
    
    // 關閉對話框
    showDeleteConfirmDialog.value = false;
    roomIdToDelete.value = null;
};

// 取消刪除
const cancelDelete = () => {
    showDeleteConfirmDialog.value = false;
    roomIdToDelete.value = null;
};

// 監聽選項變更
watch(() => searchParams.portId, (newValue) => {
    watchPortChange();
    searchRooms();
});

watch(() => searchParams.officeId, (newValue) => {
    watchOfficeChange();
    searchRooms();
});

watch(() => searchParams.floor, (newValue) => {
    watchFloorChange();
    searchRooms();
});

watch(() => searchParams.roomId, (newValue) => {
    searchRooms();
});

// 添加編輯模態視窗相關狀態
const showEditModal = ref(false);
const selectedRoom = ref(null);

// 保存會議室資料
const saveRoom = async (room) => {
    console.log('保存會議室資料:', room);
    
    try {
        // 根據是否有ID決定是新增還是修改
        const apiUrl = room.id ? '/api/manage/update' : '/api/manage/create';
        
        await api.post(apiUrl, room, {
            callback: function(response) {
                if (response.data && response.data.statusCode === '0000') {
                    toast.success(room.id ? '會議室資料已更新' : '會議室已新增', {
                        timeout: 2000,
                        onClose: async () => {
                            closeEditModal();
                            
                            // 先重新載入選項資料
                            await loadOptions();
                            
                            // 如果是修改操作且修改的是當前選中的會議室，則重置會議室選擇
                            if (room.id && searchParams.roomId === room.id) {
                                searchParams.roomId = '';
                            }
                            
                            // 重新搜尋會議室列表
                            searchRooms();
                        }
                    });
                } else {
                    toast.error('操作失敗: ' + (response.data?.statusMessage || '未知錯誤'), {
                        timeout: 3000
                    });
                }
            }
        });
    } catch (error) {
        console.error('保存會議室資料失敗:', error);
        toast.error('操作失敗: ' + (error.message || '未知錯誤'), {
            timeout: 3000
        });
    }
};

// 關閉編輯模態視窗
const closeEditModal = () => {
    showEditModal.value = false;
    selectedRoom.value = null;
};
</script>

<style scoped>
.form-select {
    background-color: #2c3034;
    color: #f8f9fa;
    border-color: #495057;
}

.form-select:focus {
    border-color: #6c757d;
    box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.25);
}

.search-results-container {
    min-height: 450px; /* 約10筆資料的高度 */
}

.table {
    margin-top: 1rem;
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th, .table td {
    vertical-align: middle;
    height: 45px; /* 設定每行高度 */
}

.empty-row {
    background-color: rgba(33, 37, 41, 0.5) !important;
}

.empty-row:hover {
    background-color: rgba(33, 37, 41, 0.5) !important;
    cursor: default;
}

.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 主題切換開關樣式 */
.theme-toggle {
    margin-right: 10px;
}

.form-check-input {
    cursor: pointer;
}

/* 深色卡片樣式 - 默認 */
.dark-card {
    background-color: transparent !important;
    border-color: rgba(255, 255, 255, 0.175) !important;
}

/* 淺色卡片樣式 */
.light-card {
    background-color: #ffffff !important;
    border-color: #dee2e6 !important;
}

/* 淺色表格樣式 */
.light-table .table-dark {
    --bs-table-bg: #ffffff;
    --bs-table-striped-bg: #f8f9fa;
    --bs-table-striped-color: #212529;
    --bs-table-active-bg: #dee2e6;
    --bs-table-active-color: #212529;
    --bs-table-hover-bg: #e9ecef;
    --bs-table-hover-color: #212529;
    color: #212529;
    border-color: #dee2e6;
}

.light-table .table-dark th,
.light-table .table-dark td {
    border-color: #dee2e6;
    color: #212529;
}

.light-table .text-secondary {
    color: #6c757d !important;
}

/* 深色表格樣式 - 默認 */
.dark-table .table-dark {
    --bs-table-bg: #212529;
    --bs-table-striped-bg: #2c3034;
    --bs-table-striped-color: #fff;
    --bs-table-active-bg: #373b3e;
    --bs-table-active-color: #fff;
    --bs-table-hover-bg: #323539;
    --bs-table-hover-color: #fff;
    color: #fff;
    border-color: #373b3e;
}

/* 淺色模式下的按鈕樣式調整 */
.light-table .btn-outline-info {
    color: #0dcaf0;
    border-color: #0dcaf0;
}

.light-table .btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}
</style>
