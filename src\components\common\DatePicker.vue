<template>
  <div class="date-picker-container">
    <input 
      type="text" 
      class="form-control date-input" 
      :value="modelValue ? formatDisplayDate(modelValue) : ''" 
      @input="handleManualInput"
      @click="handleInputClick"
      :class="inputClass"
      :placeholder="placeholder || '選擇日期'"
      :disabled="disabled"
    >
    <div v-if="showPicker && !disabled" class="date-picker-dropdown" :class="dropdownClass">
      <div class="date-picker-header">
        <button type="button" @click.stop="changeMonth(-1)" class="date-nav-btn">&lt;</button>
        <span>{{ calendarTitle }}</span>
        <button type="button" @click.stop="changeMonth(1)" class="date-nav-btn">&gt;</button>
      </div>
      <div class="date-picker-weekdays">
        <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
      </div>
      <div class="date-picker-days">
        <div 
          v-for="(day, index) in calendarDays" 
          :key="index" 
          class="day-cell" 
          :class="{
            'other-month': day.otherMonth,
            'current-day': day.isToday,
            'selected-day': day.isSelected,
            'disabled-day': isDisabledDate(day.date)
          }"
          @click.stop="!isDisabledDate(day.date) && selectDate(day.date)"
        >
          {{ day.day }}
        </div>
      </div>
      <div class="date-picker-footer">
        <button type="button" class="btn btn-link btn-sm text-decoration-none" @click.stop="clearDate">清除</button>
        <button type="button" class="btn btn-link btn-sm text-decoration-none" @click.stop="selectToday">今天</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { zhTW } from 'date-fns/locale';

const props = defineProps({
  modelValue: [Date, String],
  placeholder: String,
  disabled: Boolean,
  minDate: [Date, String],
  maxDate: [Date, String],
  inputClass: String,
  dropdownClass: String,
  locale: {
    type: Object,
    default: () => zhTW
  }
});

const emit = defineEmits(['update:modelValue']);

const showPicker = ref(false);
const currentViewDate = ref(props.modelValue ? new Date(props.modelValue) : new Date());

// 星期幾的顯示
const weekdays = ['週一', '週二', '週三', '週四', '週五', '週六', '週日'];

// 獲取月份名稱
const currentMonthName = computed(() => {
  const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
  return monthNames[currentViewDate.value.getMonth()];
});

// 轉換為民國年
const toRocYear = (year) => {
  return `民國 ${year - 1911}`;
};

// 計算日曆標題 (民國年)
const calendarTitle = computed(() => {
  const year = currentViewDate.value.getFullYear();
  const month = currentMonthName.value;
  // 修改標題格式為「114 年 六 月」，移除「度」字
  return `${year - 1911} 年 ${month.charAt(0)} 月`;
});

// 處理輸入框點擊事件
function handleInputClick(event) {
  event.stopPropagation(); // 阻止事件冒泡
  
  if (!props.disabled) {
    showPicker.value = !showPicker.value;
    console.log('DatePicker input clicked, disabled:', props.disabled, 'showPicker:', showPicker.value);
  }
}

// 計算日曆天數
const calendarDays = computed(() => {
  const year = currentViewDate.value.getFullYear();
  const month = currentViewDate.value.getMonth();
  
  // 當月第一天
  const firstDay = new Date(year, month, 1);
  // 當月最後一天
  const lastDay = new Date(year, month + 1, 0);
  
  // 獲取當月第一天是星期幾（0-6，0表示週日）
  let firstDayOfWeek = firstDay.getDay();
  // 調整為週一為第一天（0表示週一，6表示週日）
  firstDayOfWeek = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;
  
  // 當月天數
  const daysInMonth = lastDay.getDate();
  
  // 上個月的最後幾天
  const prevMonthLastDay = new Date(year, month, 0).getDate();
  
  // 生成日曆數據
  const days = [];
  
  // 添加上個月的日期
  for (let i = 0; i < firstDayOfWeek; i++) {
    const day = prevMonthLastDay - firstDayOfWeek + i + 1;
    const date = new Date(year, month - 1, day);
    days.push({
      day,
      date,
      otherMonth: true,
      isToday: isSameDay(date, new Date()),
      isSelected: props.modelValue && isSameDay(date, new Date(props.modelValue))
    });
  }
  
  // 添加當月的日期
  for (let i = 1; i <= daysInMonth; i++) {
    const date = new Date(year, month, i);
    days.push({
      day: i,
      date,
      otherMonth: false,
      isToday: isSameDay(date, new Date()),
      isSelected: props.modelValue && isSameDay(date, new Date(props.modelValue))
    });
  }
  
  // 添加下個月的日期，填滿6行
  const totalCells = 6 * 7; // 6行7列
  const remainingCells = totalCells - days.length;
  
  for (let i = 1; i <= remainingCells; i++) {
    const date = new Date(year, month + 1, i);
    days.push({
      day: i,
      date,
      otherMonth: true,
      isToday: isSameDay(date, new Date()),
      isSelected: props.modelValue && isSameDay(date, new Date(props.modelValue))
    });
  }
  
  return days;
});

// 判斷兩個日期是否是同一天
function isSameDay(date1, date2) {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
}

// 格式化日期為顯示格式
function formatDisplayDate(date) {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const rocYear = year - 1911; // 轉換為民國年
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  // 修改輸入框中的格式，移除「民國」二字
  return `${rocYear} 年 ${month} 月 ${day} 日`;
}

// 格式化日期為 YYYY-MM-DD
function formatDateString(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// 選擇日期
function selectDate(date) {
  if (isDisabledDate(date)) return;
  
  emit('update:modelValue', formatDateString(date));
  showPicker.value = false;
  console.log('Date selected:', formatDateString(date));
}

// 清除日期
function clearDate() {
  emit('update:modelValue', '');
  showPicker.value = false;
}

// 選擇今天
function selectToday() {
  const today = new Date();
  emit('update:modelValue', formatDateString(today));
  currentViewDate.value = new Date(today);
  showPicker.value = false;
}

// 切換月份
function changeMonth(delta) {
  const newDate = new Date(currentViewDate.value);
  newDate.setMonth(newDate.getMonth() + delta);
  currentViewDate.value = newDate;
}

// 檢查日期是否被禁用
function isDisabledDate(date) {
  // 如果沒有設置最小日期和最大日期，則不禁用任何日期
  if (!props.minDate && !props.maxDate) {
    return false;
  }
  
  // 檢查是否有最小日期限制
  if (props.minDate) {
    const minDate = new Date(props.minDate);
    minDate.setHours(0, 0, 0, 0); // 設置為當天的開始時間
    if (date < minDate) return true;
  }
  
  // 檢查是否有最大日期限制
  if (props.maxDate) {
    const maxDate = new Date(props.maxDate);
    maxDate.setHours(0, 0, 0, 0); // 設置為當天的開始時間
    if (date > maxDate) return true;
  }
  
  return false; // 如果沒有限制或日期在限制範圍內，則不禁用
}

// 監聽 modelValue 變化
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    currentViewDate.value = new Date(newValue);
  }
});

// 點擊外部關閉日期選擇器
function handleOutsideClick(e) {
  const datePickerContainer = e.target.closest('.date-picker-container');
  
  if (!datePickerContainer && showPicker.value) {
    showPicker.value = false;
  }
}

onMounted(() => {
  document.addEventListener('click', handleOutsideClick);
});

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick);
});

// 處理手動輸入
function handleManualInput(event) {
  const inputValue = event.target.value;
  
  // 嘗試解析輸入的日期格式
  // 支援格式：「114 年 06 月 13 日」或「114/06/13」或「114-06-13」
  let year, month, day;
  
  // 嘗試匹配「114 年 06 月 13 日」格式
  const formatOne = /^(\d{1,3})\s*年\s*(\d{1,2})\s*月\s*(\d{1,2})\s*日$/;
  // 嘗試匹配「114/06/13」或「114-06-13」格式
  const formatTwo = /^(\d{1,3})[/-](\d{1,2})[/-](\d{1,2})$/;
  
  let match = inputValue.match(formatOne);
  if (match) {
    [, year, month, day] = match;
  } else {
    match = inputValue.match(formatTwo);
    if (match) {
      [, year, month, day] = match;
    }
  }
  
  if (match) {
    // 將民國年轉換為西元年
    const westernYear = parseInt(year) + 1911;
    month = parseInt(month);
    day = parseInt(day);
    
    // 檢查日期是否有效
    if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
      const date = new Date(westernYear, month - 1, day);
      
      // 確認日期是否有效（例如，2月30日是無效的）
      if (date.getFullYear() === westernYear && 
          date.getMonth() === month - 1 && 
          date.getDate() === day) {
        
        // 格式化為 YYYY-MM-DD 並更新模型值
        emit('update:modelValue', formatDateString(date));
        return;
      }
    }
  }
  
  // 如果輸入無效，恢復為原始值
  event.target.value = modelValue ? formatDisplayDate(modelValue) : '';
}
</script>

<style scoped>
.date-picker-container {
  position: relative;
  z-index: 100; /* 確保日期選擇器在其他元素之上 */
}

/* 修正日期選擇器輸入框樣式 */
.date-input {
  cursor: pointer;
  background-color: inherit; /* 繼承父元素背景色 */
}

/* 深色模式下的日期選擇器輸入框 */
.dark-input.date-input {
  background-color: #343a40 !important;
  border-color: rgba(255, 255, 255, 0.175) !important;
  color: #fff !important; /* 確保文字為白色 */
}

/* 淺色模式下的日期選擇器輸入框 */
.light-input.date-input {
  background-color: #fff !important;
  border-color: #ced4da !important;
  color: #212529 !important;
}

/* 確保輸入框中的文字顏色正確 */
.date-input::placeholder {
  color: inherit;
  opacity: 0.7;
}

/* 確保輸入框中的文字在深色模式下為白色 */
.dark-input.date-input::placeholder {
  color: rgba(255, 255, 255, 0.7) !important;
}

.date-input:disabled {
  cursor: not-allowed;
  background-color: #e9ecef;
}

.date-picker-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000; /* 提高 z-index 確保日曆顯示在最上層 */
  width: 280px;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  padding: 0.5rem;
  margin-top: 0.25rem;
}

.date-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.25rem 0;
  font-weight: bold;
  text-align: center;
}

.date-nav-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0 0.5rem;
  color: inherit;
}

.date-picker-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-weight: bold;
  margin-bottom: 0.25rem;
  font-size: 0.8rem;
}

.weekday {
  padding: 0.25rem;
}

.date-picker-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-gap: 1px;
}

.day-cell {
  text-align: center;
  padding: 0.4rem;
  cursor: pointer;
  border-radius: 0.25rem;
  font-size: 0.9rem;
}

.day-cell:hover:not(.disabled-day) {
  background-color: #f8f9fa;
}

.other-month {
  color: #adb5bd;
}

.current-day {
  font-weight: bold;
}

.selected-day {
  background-color: #00c853 !important; /* 使用綠色，與圖片中的顏色一致 */
  color: white !important;
  border-radius: 50%; /* 使用圓形樣式 */
}

.disabled-day {
  color: #dee2e6;
  cursor: not-allowed;
}

.date-picker-footer {
  display: flex;
  justify-content: space-between;
  padding-top: 0.5rem;
  border-top: 1px solid #dee2e6;
  margin-top: 0.5rem;
}

/* 調整標題樣式以適應新的格式 */
.date-picker-header span {
  flex: 1;
  text-align: center;
  font-size: 0.95rem;
  letter-spacing: 1px;
}

/* 調整星期標題樣式 */
.date-picker-weekdays .weekday {
  color: #666;
  font-size: 0.75rem;
}

/* 調整日期單元格大小，使其更接近圖片中的樣式 */
.day-cell {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2px;
}

/* 調整今天的樣式 */
.current-day {
  font-weight: bold;
  color: #00c853; /* 使用綠色標記今天 */
}

/* 調整按鈕樣式 */
.date-picker-footer .btn-link {
  color: #00c853; /* 使用綠色按鈕文字 */
  padding: 0;
}
</style>
