
<template>
    <MainLayout>
        <template #title>
            <span class="text-light fs-3 fw-bold">會議查詢</span>
        </template>
        <template #subTitle>
            <span class="text-white-50">搜尋當前所有會議室資訊</span>
        </template>
        <template #content>
            <div class="card bg-transparent border-light rounded-4 p-4 mb-4">
                <div class="row">
                    <div class="col-12">
                        <h5 class="text-light mb-3">搜尋條件</h5>
                        
                        <!-- 查詢條件區域 -->
                        <div class="row g-3 align-items-center">
                            <!-- 預約單位 -->
                            <div class="col-md-3">
                                <label for="departmentSelect" class="form-label text-light">預約單位</label>
                                <select id="departmentSelect" class="form-select" v-model="searchParams.department">
                                    <option value="">請選擇</option>
                                    <option v-for="department in departmentOptions" :key="department.id" :value="department.id">
                                        {{ department.cname }}
                                    </option>
                                </select>
                            </div>
                            
                            <!-- 樓層 -->
                            <div class="col-md-2">
                                <label for="floorSelect" class="form-label text-light">樓層</label>
                                <select id="floorSelect" class="form-select" v-model="searchParams.floor">
                                    <option value="">請選擇</option>
                                    <option v-for="floor in floorOptions" :key="floor.id" :value="floor.id">
                                        {{ floor.floorName }}
                                    </option>
                                </select>
                            </div>
                            
                            <!-- 會議室 -->
                            <div class="col-md-2">
                                <label for="roomSelect" class="form-label text-light">會議室</label>
                                <select id="roomSelect" class="form-select" v-model="searchParams.room">
                                    <option value="">請選擇</option>
                                    <option v-for="room in roomOptions" :key="room.id" :value="room.id">
                                        {{ room.roomName }}
                                    </option>
                                </select>
                            </div>
                            
                            <!-- 開始日期 -->
                            <div class="col-md-2">
                                <label for="startDate" class="form-label text-light">開始日期</label>
                                <DatePicker id="startDate"
                                    v-model="searchParams.startDate" 
                                    input-class="dark-input"
                                    placeholder="選擇日期"
                                />
                            </div>
                            
                            <!-- 結束日期 -->
                            <div class="col-md-2">
                                <label for="endDate" class="form-label text-light">結束日期</label>
                                <DatePicker id="endDate"
                                    v-model="searchParams.endDate" 
                                    input-class="dark-input"
                                    placeholder="選擇日期"
                                />
                            </div>
                            
                            <!-- 清除按鈕 -->
                            <button class="btn btn-outline-light rounded-pill col-md-1 mt-5" @click="clearSearch">清除</button>
                        </div>
                    </div>
                </div>
            </div>
                
            <div class="card bg-transparent border-light rounded-4 p-4 results-card" :class="{'light-card': !isDarkMode, 'dark-card': isDarkMode}">
                <div class="row">        
                    <!-- 搜尋結果區域 -->
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <h5 class="result-title" :class="{'text-light': isDarkMode, 'text-dark': !isDarkMode}" Style="display: flex; align-items: center;gap: 20px;">搜尋結果<input type="text" class="form-control" style="width: 300px;" :style="{'background-color': isDarkMode ? '#343a40' : '#fff', 'border-color': isDarkMode ? 'rgba(255, 255, 255, 0.175)' : '#ced4da', 'color': isDarkMode ? '#fff' : '#212529'}" id="searchInput" v-model="searchParams.searchText" @keyup.enter="searchMeetings" placeholder="請輸入關鍵字搜尋會議主題"></h5>
                            
                            <div class="theme-toggle d-flex align-items-center">
                                <span class="me-2 small" :class="{'text-light': isDarkMode, 'text-dark': !isDarkModeisDarkMode}">{{ isDarkMode ? '深色版' : '淺色版' }}</span>
                                <div class="form-check form-switch mb-0">
                                    <input class="form-check-input" type="checkbox" id="themeSwitch" v-model="isDarkMode" @change="toggleTheme">
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive search-results-container" :class="{'light-table': !isDarkMode, 'dark-table': isDarkMode}">
                            <table class="table table-dark table-hover">
                                <thead>
                                    <tr>
                                        <th class="text-secondary" @click="sortTable('subject')">
                                            會議主題
                                            <span class="sort-icon">{{ getSortIcon('subject') }}</span>
                                        </th>
                                        <th class="text-secondary" style="width: 160px;" @click="sortTable('room_name')">
                                            會議室
                                            <span class="sort-icon">{{ getSortIcon('room_name') }}</span>
                                        </th>
                                        <th class="text-secondary" style="width: 100px;" @click="sortTable('host_name')">
                                            主持人
                                            <span class="sort-icon">{{ getSortIcon('host_name') }}</span>
                                        </th>
                                        <th class="text-secondary" style="width: 180px;" @click="sortTable('department_name')">
                                            預約單位
                                            <span class="sort-icon">{{ getSortIcon('department_name') }}</span>
                                        </th>
                                        <th class="text-secondary" style="width: 140px;" @click="sortTable('contact_name')">
                                            聯&nbsp;絡&nbsp;人<br><font class="small text-secondary">聯絡電話</font>
                                            <span class="sort-icon">{{ getSortIcon('contact_name') }}</span>
                                        </th>
                                        <th class="text-secondary text-center text-nowrap" @click="sortTable('reserveDate')">
                                            預約日期
                                            <span class="sort-icon">{{ getSortIcon('reserveDate') }}</span>
                                        </th>
                                        <th class="text-secondary text-center text-nowrap" style="width: 130px;" @click="sortTable('reserveTime')">
                                            預約時段
                                            <span class="sort-icon">{{ getSortIcon('reserveTime') }}</span>
                                        </th>
                                        <th class="text-secondary text-center" style="width: 120px;" @click="sortTable('status')">
                                            使用狀態
                                            <span class="sort-icon">{{ getSortIcon('status') }}</span>
                                        </th>
                                        <th class="text-secondary text-center" style="width: 80px;">功能</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(meeting, index) in searchResults" :key="index">
                                        <td>{{ meeting.subject }}</td>
                                        <td>{{ meeting.roomName }}</td>
                                        <td>{{ meeting.hostName }}</td>
                                        <td>{{ meeting.departmentName }}</td>
                                        <td>{{ meeting.contactName }}<br><font class="small text-secondary">{{ meeting.contactPhone }}</font></td>
                                        <td class="text-center text-nowrap"><small>{{ formatToRocDate(meeting.reserveDate) }}</small></td>
                                        <td class="text-center text-nowrap"><small>{{ meeting.reserveDateStart }} ~ {{ meeting.reserveDateEnd }}</small></td>
                                        <td class="text-center text-nowrap">{{ meeting.status }}</td>
                                        <td>
                                            <div class="d-flex justify-content-around">
                                                <button class="btn btn-action" title="查看詳情" :disabled="meeting.isPublic === false" @click="viewMeetingDetail(meeting)">
                                                    <i class="fa fa-search" :style="{ color: meeting.isPublic ? '#28a745' : '#3d3d3d' }"></i>
                                                </button>
                                                <button class="btn btn-action" title="簽到記錄" @click="viewAttendanceRecord(meeting)">
                                                    <i class="fa fa-file"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>    
            </div>
            <!-- 在搜尋結果表格下方添加分頁條 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <!-- 每頁筆數選單 -->
                        <div class="d-flex align-items-center">
                            <span class="text-light me-2 small">每頁顯示：</span>
                            <select class="form-select form-select-sm page-size-select" v-model="pageSize" @change="changePageSize">
                                <option value="10">10</option>
                                <option value="20">20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                        
                        <!-- 分頁條 -->
                        <nav aria-label="會議記錄分頁">
                            <ul class="pagination pagination-sm mb-0">
                                <!-- 首頁按鈕 -->
                                <li class="page-item" :class="{disabled: currentPage === 1}">
                                    <a class="page-link" href="#" @click.prevent="goToFirstPage" :aria-disabled="currentPage === 1">
                                        <span>&laquo;</span>
                                    </a>
                                </li>
                                <!-- 上一頁按鈕 -->
                                <li class="page-item" :class="{disabled: currentPage === 1}">
                                    <a class="page-link" href="#" @click.prevent="goToPrevPage" :aria-disabled="currentPage === 1">
                                        <span>&lsaquo;</span>
                                    </a>
                                </li>
                                <!-- 頁碼按鈕 -->
                                <li v-for="page in pageNumbers" :key="page" class="page-item" :class="{active: page === currentPage}">
                                    <a class="page-link" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                                </li>
                                <!-- 下一頁按鈕 -->
                                <li class="page-item" :class="{disabled: currentPage === totalPages}">
                                    <a class="page-link" href="#" @click.prevent="goToNextPage" :aria-disabled="currentPage === totalPages">
                                        <span>&rsaquo;</span>
                                    </a>
                                </li>
                                <!-- 尾頁按鈕 -->
                                <li class="page-item" :class="{disabled: currentPage === totalPages}">
                                    <a class="page-link" href="#" @click.prevent="goToLastPage" :aria-disabled="currentPage === totalPages">
                                        <span>&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        
                        <!-- 右側空白區域，保持對稱 -->
                        <div style="width: 150px;"></div>
                    </div>
                </div>
            </div>
        </template>
    </MainLayout>
    
    <!-- 添加會議詳情模態框 -->
    <MeetingDetailModal
        v-if="showDetailModal"
        :meeting="selectedMeeting"
        :visible="showDetailModal"
        @close="showDetailModal = false"
    />
    
    <!-- 添加簽到記錄模態框 -->
    <AttendanceRecordModal
        v-if="showAttendanceModal"
        @close="showAttendanceModal = false"
    />
</template>

<script setup>
import MainLayout from '../components/MainLayout.vue';
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue';
import api from '../utils/api';
import MeetingDetailModal from '../components/MeetingDetailModal.vue';
import AttendanceRecordModal from '../components/AttendanceRecordModal.vue';
import DatePicker from '../components/common/DatePicker.vue';
import { formatToRocDateTime, formatToRocDate } from '../utils/dateUtils';

// 查詢參數
const searchParams = reactive({
    department: '',
    floor: '',
    room: '',
    startDate: '',
    endDate: '',
    searchText: '',
    sorts: [
        {field: 'room_name', direction: 'asc'}
    ],
    page: 1,
    limit: 10
});

// 搜尋結果
const searchResults = ref([]);
const hasSearched = ref(false);

// 主題設置
const isDarkMode = ref(true); // 預設為深色模式

// 分頁相關狀態
const pageSize = ref(10); // 每頁顯示筆數
const currentPage = ref(1); // 當前頁碼
const totalItems = ref(0); // 總記錄數
const totalPages = ref(0); // 總頁數

// 排序相關狀態
const sortField = ref('room_name'); // 當前排序欄位
const sortDirection = ref('asc'); // 排序方向：asc 或 desc

// 選項數據
const departmentOptions = ref([]);
const floorOptions = ref([]);
const roomOptions = ref([]);

// 會議詳情模態框相關狀態
const showDetailModal = ref(false);
const selectedMeeting = ref({});

// 簽到記錄模態框相關狀態
const showAttendanceModal = ref(false);

// 查看會議詳細資訊
const viewMeetingDetail = (meeting) => {
    console.log('查看會議詳情:', meeting);
    selectedMeeting.value = meeting;
    showDetailModal.value = true;
};

// 查看簽到記錄
const viewAttendanceRecord = (meeting) => {
    console.log('查看簽到記錄:', meeting);
    showAttendanceModal.value = true;
};

// 排序方法
const sortTable = (field) => {
    // 如果點擊的是當前排序欄位，則切換排序方向
    if (field === sortField.value) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    } else {
        // 如果點擊的是新欄位，則設置為該欄位升序排序
        sortField.value = field;
        sortDirection.value = 'asc';
    }
    
    // 更新排序參數
    const fieldMapping = {
        'subject': 'subject',
        'room_name': 'room_name',
        'host_name': 'host_name',
        'department_name': 'department_name',
        'contact_name': 'contact_name',
        'reserveDate': 'reserve_date',
        'reserveTime': 'reserve_date_start',
        'status': 'status'
    };
    
    // 更新搜尋參數中的排序設定
    searchParams.sorts = [{
        field: fieldMapping[field] || field,
        direction: sortDirection.value
    }];
    
    console.log(`排序欄位: ${field}, 方向: ${sortDirection.value}`);
    console.log('排序參數:', searchParams.sorts);
    
    // 重新查詢資料
    searchMeetings();
    
    // 更新表頭的 data-sorted 屬性
    nextTick(() => {
        // 先移除所有表頭的 data-sorted 屬性
        const headers = document.querySelectorAll('th');
        headers.forEach(header => {
            header.removeAttribute('data-sorted');
        });
        
        // 設置當前排序欄位的 data-sorted 屬性
        const fieldMap = {
            'subject': 0,
            'room_name': 1,
            'host_name': 2,
            'department_name': 3,
            'contact_name': 4,
            'reserveDate': 5,
            'reserveTime': 6,
            'status': 7
        };
        
        if (fieldMap[field] !== undefined) {
            const headerIndex = fieldMap[field];
            const header = headers[headerIndex];
            if (header) {
                header.setAttribute('data-sorted', 'true');
                console.log(`已設置欄位 ${field} (索引 ${headerIndex}) 的 data-sorted 屬性`);
            } else {
                console.error(`找不到索引為 ${headerIndex} 的表頭元素`);
            }
        }
    });
};

// 獲取排序圖標
const getSortIcon = (field) => {
    if (field !== sortField.value) return '⇅'; // 未排序：雙向箭頭
    return sortDirection.value === 'asc' ? '↑' : '↓'; // 升序：向上箭頭，降序：向下箭頭
};

// 計算需要填充的空行數量
const emptyRowsCount = computed(() => {
    const resultCount = searchResults.value.length;
    return resultCount < 10 ? 10 - resultCount : 0;
});

// 初始化頁面時執行
onMounted(() => {
    // 初始化主題
    initTheme();
    
    // 設置初始排序欄位
    sortField.value = 'room_name';
    sortDirection.value = 'asc';
    
    // 載入會議室選項
    loadRoomOptions();
    
    // 載入預約單位選項
    loadDepartmentOptions();
    
    // 進入頁面後立即執行搜尋
    searchMeetings();
    
    // 確保初始排序欄位的樣式正確應用
    nextTick(() => {
        applyInitialSortStyle();
    });
});

// 初始化主題
const initTheme = () => {
    const savedTheme = localStorage.getItem('tableTheme');
    if (savedTheme !== null) {
        isDarkMode.value = savedTheme === 'true';
    }
    applyTheme();
};

// 切換主題
const toggleTheme = () => {
    localStorage.setItem('tableTheme', isDarkMode.value);
    applyTheme();
};

// 應用主題
const applyTheme = () => {
    const tableContainer = document.querySelector('.search-results-container');
    if (tableContainer) {
        if (isDarkMode.value) {
            tableContainer.classList.add('dark-table');
            tableContainer.classList.remove('light-table');
        } else {
            tableContainer.classList.add('light-table');
            tableContainer.classList.remove('dark-table');
        }
    }
};

// 搜尋會議
const searchMeetings = async () => {
    try {
        const params = {
            ...searchParams,
            page: currentPage.value,
            limit: pageSize.value,
        };
        
        await api.post('/api/record/search', params, {
            callback: function(response) {
                if (response.data?.data) {
                    searchResults.value = response.data.data.data || [];
                    totalItems.value = response.data.data.total || 0;
                    totalPages.value = response.data.data.totalPage || 0;
                    hasSearched.value = true;
                    // 確保在數據加載後應用主題
                    setTimeout(applyTheme, 0);
                } else {
                    searchResults.value = [];
                    totalItems.value = 0;
                    totalPages.value = 0;
                }
            }
        });
    } catch (error) {
        console.error('搜尋會議失敗:', error);
        searchResults.value = [];
        totalItems.value = 0;
        totalPages.value = 0;
    }
};

// 清除搜尋條件
const clearSearch = () => {
    searchParams.department = '';
    searchParams.floor = '';
    searchParams.room = '';
    searchParams.startDate = '';
    searchParams.endDate = '';
    searchParams.searchText = '';
    // 重置排序為預設值
    searchParams.sorts = [{field: 'room_name', direction: 'asc'}];
    sortField.value = 'room_name';
    sortDirection.value = 'asc';
    currentPage.value = 1; // 重置為第一頁
    searchMeetings(); // 清除後重新搜尋
    
    // 清除表頭的排序標記
    nextTick(() => {
        const headers = document.querySelectorAll('th');
        headers.forEach(header => {
            header.removeAttribute('data-sorted');
        });
        applyInitialSortStyle();
    });
};

// 格式化日期時間
const formatDateTime = (dateTimeStr) => {
    if (!dateTimeStr) return '';
    const date = new Date(dateTimeStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 監聽選項變更
watch(() => searchParams.department, searchMeetings); // 當選項變更時自動搜尋
watch(() => searchParams.floor, searchMeetings); // 當選項變更時自動搜尋
watch(() => searchParams.room, searchMeetings); // 當選項變更時自動搜尋
watch(() => searchParams.startDate, searchMeetings); // 當選項變更時自動搜尋
watch(() => searchParams.endDate, searchMeetings); // 當選項變更時自動搜尋
watch(() => searchParams.searchText, searchMeetings); // 當選項變更時自動搜尋

// 變更每頁顯示筆數
const changePageSize = () => {
    console.log('每頁顯示筆數變更為:', pageSize.value);
    currentPage.value = 1; // 重置為第一頁
    searchMeetings(); // 重新搜尋
};

// 變更頁碼
const changePage = (page) => {
    if (page < 1 || page > totalPages.value || page === currentPage.value) return;
    
    console.log('頁碼變更為:', page);
    currentPage.value = page;
    searchMeetings(); // 重新搜尋
};

// 跳轉到第一頁
const goToFirstPage = () => {
    if (currentPage.value === 1) return;
    changePage(1);
};

// 跳轉到上一頁
const goToPrevPage = () => {
    if (currentPage.value > 1) {
        changePage(currentPage.value - 1);
    }
};

// 跳轉到下一頁
const goToNextPage = () => {
    if (currentPage.value < totalPages.value) {
        changePage(currentPage.value + 1);
    }
};

// 跳轉到最後一頁
const goToLastPage = () => {
    if (currentPage.value === totalPages.value) return;
    changePage(totalPages.value);
};

// 計算要顯示的頁碼
const pageNumbers = computed(() => {
    const pages = [];
    const maxVisiblePages = 5; // 最多顯示5個頁碼
    
    let startPage = Math.max(1, currentPage.value - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;
    
    if (endPage > totalPages.value) {
        endPage = totalPages.value;
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
    }
    
    return pages;
});

// 應用初始排序樣式
const applyInitialSortStyle = () => {
    // 先移除所有表頭的 data-sorted 屬性
    const headers = document.querySelectorAll('th');
    headers.forEach(header => {
        header.removeAttribute('data-sorted');
    });
    
    // 設置當前排序欄位的 data-sorted 屬性
    const fieldMap = {
        'subject': 0,
        'room_name': 1,
        'host_name': 2,
        'department_name': 3,
        'contact_name': 4,
        'reserveDate': 5,
        'reserveTime': 6,
        'status': 7
    };
    
    if (fieldMap[sortField.value] !== undefined) {
        const headerIndex = fieldMap[sortField.value];
        const header = headers[headerIndex];
        if (header) {
            header.setAttribute('data-sorted', 'true');
            console.log(`初始化：已設置欄位 ${sortField.value} (索引 ${headerIndex}) 的 data-sorted 屬性`);
        } else {
            console.error(`初始化：找不到索引為 ${headerIndex} 的表頭元素`);
        }
    }
};

// 載入預約單位選項
const loadDepartmentOptions = async () => {
    try {
        await api.get('/api/ui/parent/departments', {
            callback: function(response) {
                if (response.data && Array.isArray(response.data)) {
                    departmentOptions.value = response.data;
                    console.log('成功載入預約單位選項:', departmentOptions.value);
                } else {
                    console.error('載入預約單位選項失敗: 回應格式不正確');
                }
            }
        });
    } catch (error) {
        console.error('載入預約單位選項時發生錯誤:', error);
    }
};

// 載入會議室選項
const loadRoomOptions = async () => {
    try {
        console.log('開始載入會議室選項...');
        await api.get('/api/ui/rooms', {
            callback: function(response) {
                console.log('會議室選項 API 回應:', response);
                if (response.data) {
                    floorOptions.value = response.data;
                    // 初始化時載入所有會議室
                    roomOptions.value = response.data.flatMap(floor => floor.rooms || []);
                    console.log('成功載入樓層和會議室選項:', floorOptions.value);
                } else {
                    console.error('載入會議室選項失敗: 回應格式不正確');
                }
            }
        });
    } catch (error) {
        console.error('載入會議室選項時發生錯誤:', error);
    }
};

// 監聽樓層變更，更新會議室選項
watch(() => searchParams.floor, (newFloor) => {
    if (!newFloor) {
        // 如果未選擇樓層，顯示所有會議室
        roomOptions.value = floorOptions.value.flatMap(floor => floor.rooms || []);
        return;
    }
    
    // 找到選中的樓層
    const selectedFloor = floorOptions.value.find(floor => floor.id === newFloor);
    
    // 更新會議室選項
    if (selectedFloor && selectedFloor.rooms) {
        roomOptions.value = selectedFloor.rooms;
        console.log(`已更新會議室選項為樓層 ${newFloor} 的會議室:`, roomOptions.value);
    } else {
        console.error(`找不到樓層 ID 為 ${newFloor} 的樓層或其會議室`);
        roomOptions.value = [];
    }
});

// 監聽會議室變更，自動選擇對應的樓層
watch(() => searchParams.room, (newRoom) => {
    if (!newRoom) return;
    
    // 尋找該會議室所屬的樓層
    for (const floor of floorOptions.value) {
        if (!floor.rooms) continue;
        
        const roomExists = floor.rooms.some(room => room.id === newRoom);
        if (roomExists) {
            // 如果找到會議室所屬的樓層，且當前選擇的樓層不是該樓層，則更新樓層選擇
            if (searchParams.floor !== floor.id) {
                console.log(`會議室 ${newRoom} 屬於樓層 ${floor.id}，自動更新樓層選擇`);
                searchParams.floor = floor.id;
            }
            return;
        }
    }
    
    console.error(`找不到會議室 ID 為 ${newRoom} 所屬的樓層`);
});
</script>

<style scoped>
.form-select, .form-control {
    background-color: #2c3034;
    color: #f8f9fa;
    border-color: #495057;
}

.form-select:focus, .form-control:focus {
    border-color: #6c757d;
    box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.25);
}

.input-group-text {
    color: #f8f9fa;
}

.search-results-container {
    min-height: 400px; /* 約10筆資料的高度 */
}

.table {
    margin-top: 1rem;
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th, .table td {
    vertical-align: middle;
    height: 45px; /* 設定每行高度 */
}

.empty-row {
    background-color: rgba(33, 37, 41, 0.5) !important;
}

.empty-row:hover {
    background-color: rgba(33, 37, 41, 0.5) !important;
    cursor: default;
}

.btn-outline-light {
    border-radius: 20px;
}

.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

input[type="date"]::-webkit-calendar-picker-indicator {
    filter: invert(1);
}

/* 主題切換開關樣式 */
.theme-toggle {
    margin-right: 10px;
}

.form-check-input {
    cursor: pointer;
}

/* 深色卡片樣式 - 默認 */
.dark-card {
    background-color: transparent !important;
    border-color: rgba(255, 255, 255, 0.8) !important;
}

/* 淺色卡片樣式 */
.light-card {
    background-color: #ffffff !important;
    border-color: #dee2e6 !important;
}

/* 淺色表格樣式 */
.light-table .table-dark {
    --bs-table-bg: #ffffff;
    --bs-table-striped-bg: #f8f9fa;
    --bs-table-striped-color: #212529;
    --bs-table-active-bg: #dee2e6;
    --bs-table-active-color: #212529;
    --bs-table-hover-bg: #e9ecef;
    --bs-table-hover-color: #212529;
    color: #212529;
    border-color: #dee2e6;
}

.light-table .table-dark th,
.light-table .table-dark td {
    border-color: #dee2e6;
    color: #212529;
}

.light-table .text-secondary {
    color: #6c757d !important;
}

/* 深色表格樣式 - 默認 */
.dark-table .table-dark {
    --bs-table-bg: #212529;
    --bs-table-striped-bg: #2c3034;
    --bs-table-striped-color: #fff;
    --bs-table-active-bg: #373b3e;
    --bs-table-active-color: #fff;
    --bs-table-hover-bg: #323539;
    --bs-table-hover-color: #fff;
    color: #fff;
    border-color: #373b3e;
}

/* 修正跑版的分頁條樣式 */
.pagination {
    margin-bottom: 20px;
}

.pagination .page-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0;
    margin: 0 2px;
}

/* 確保文字不會折行 */
.small {
    white-space: nowrap;
    font-size: 0.875rem;
}

/* 添加按鈕樣式 */
.btn-action {
    background-color: transparent;
    border: none;
    color: #adb5bd;
    padding: 0.25rem 0.5rem;
    transition: color 0.2s;
}

.btn-action:hover {
    color: #f8f9fa;
}

.fa-search, .fa-file {
    font-size: 1rem;
}

::placeholder {
    color: #adb5bd;
}

</style>

