<template>
  <div v-if="visible" class="modal-overlay" @click.self="handleOverlayClick">
    <div class="modal-box meetings-modal">
      <div class="d-flex justify-content-between align-items-center">
        <h3 class="m-0">{{ title }}</h3>
        <div>
          <button class="btn btn-close" @click="close" aria-label="Close"></button>
        </div>
      </div>
      
      <div class="meetings-list mt-3">
        <div v-if="meetings.length === 0" class="text-center py-4">
          <p class="text-muted">沒有會議資料</p>
        </div>
        <div v-else v-for="(meeting, index) in meetings" :key="meeting.id" 
             class="meeting-list-item" 
             :class="{'non-self-meeting': meeting.isSelf === false}"
             @click="viewMeeting(meeting)">
          <div class="meeting-number">{{ index + 1 }}</div>
          <div class="meeting-info">
            <div class="meeting-subject">{{ meeting.subject || '(無主題)' }}</div>
            <div class="meeting-details">
              <span class="meeting-room">{{ meeting.roomName || getRoomNameById(meeting.roomId) }}</span>
              <span class="meeting-time">{{ formatMeetingTime(meeting.startTime, meeting.endTime) }}</span>
            </div>
            <div class="meeting-contact" v-if="meeting.contactName">
              聯絡人: {{ meeting.contactName }} {{ meeting.contactPhone ? `(${meeting.contactPhone})` : '' }}
            </div>
          </div>
        </div>
      </div>
      
      <div class="bottom-buttons">
        <button class="btn btn-outline-secondary" @click="close">關閉</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'
import { formatToRocDateTime } from '../../utils/dateUtils'

const props = defineProps({
  visible: Boolean,
  title: {
    type: String,
    default: '會議列表'
  },
  meetings: {
    type: Array,
    default: () => []
  },
  closeOnOverlayClick: {
    type: Boolean,
    default: true
  },
  getRoomNameById: {
    type: Function,
    default: () => ''
  }
})

const emit = defineEmits(['update:visible', 'view-meeting', 'close'])

// 格式化會議時間顯示
const formatMeetingTime = (startTime, endTime) => {
  if (!startTime || !endTime) return '';
  
  // 使用 dateUtils 中的 formatToRocDateTime 函數
  const startRoc = formatToRocDateTime(startTime);
  const endRoc = formatToRocDateTime(endTime);
  
  // 如果是同一天，只顯示一次日期
  if (startTime.split(' ')[0] === endTime.split(' ')[0]) {
    // 只取結束時間的時間部分
    const endTimePart = endRoc.split(' ').slice(-1)[0];
    return `${startRoc} - ${endTimePart}`;
  } else {
    return `${startRoc} - ${endRoc}`;
  }
};

// 查看會議詳情
const viewMeeting = (meeting) => {
  emit('view-meeting', meeting);
  close();
};

// 關閉模態框
const close = () => {
  emit('update:visible', false);
  emit('close');
};

// 處理背景點擊事件
const handleOverlayClick = () => {
  if (props.closeOnOverlayClick) {
    close();
  }
};
</script>

<style scoped>
.meetings-modal {
  max-width: 700px;
  width: 90%;
}

.meetings-list {
  max-height: 60vh;
  overflow-y: auto;
  margin-bottom: 15px;
}

.meeting-list-item {
  display: flex;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  background-color: rgba(0, 123, 255, 0.2); /* 提高藍色對比度 */
  cursor: pointer;
  transition: all 0.2s ease;
}

.meeting-list-item:hover {
  background-color: rgba(0, 123, 255, 0.3); /* 提高懸停時的藍色對比度 */
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 非本人會議的樣式 */
.meeting-list-item.non-self-meeting {
  background-color: rgba(108, 117, 125, 0.1);
}

.meeting-list-item.non-self-meeting:hover {
  background-color: rgba(108, 117, 125, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.meeting-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #6c757d;
  color: white;
  border-radius: 50%;
  margin-right: 15px;
  font-weight: bold;
}

/* 本人會議的編號背景色 */
.meeting-list-item:not(.non-self-meeting) .meeting-number {
  background-color: #0d6efd; /* 保持原有的藍色 */
}

.meeting-info {
  flex: 1;
}

.meeting-subject {
  font-weight: bold;
  margin-bottom: 5px;
  color: #212529;
}

.meeting-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  color: #495057;
  font-size: 0.9rem;
}

.meeting-contact {
  color: #6c757d;
  font-size: 0.85rem;
}

.bottom-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 使用全局樣式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-box {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 20px;
  max-height: 90vh;
  overflow-y: auto;
}
</style>
