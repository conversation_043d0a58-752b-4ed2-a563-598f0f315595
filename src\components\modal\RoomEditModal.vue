<template>
  <div v-if="visible" class="modal-overlay" @click.self="checkBeforeClose">
    <div class="modal-box">
      <div class="modal-header">
        <h5 class="modal-title">{{ room.id ? '編輯會議室' : '新增會議室' }}</h5>
        <button type="button" class="btn-close" @click="checkBeforeClose" aria-label="Close"></button>
      </div>

      <div class="modal-body p-3">
        <div class="container-fluid p-0">
          <div class="alert small p-1">
            <i class="fa fa-info-circle me-1"></i> 標示 <span class="text-danger">*</span> 的欄位為必填資料，請正確填寫。
          </div>

          <div class="row">
            <!-- 左側：會議基本資訊 -->
            <div class="col-md-12">
              <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h6 class="card-title m-0 small">基本資訊</h6>
                  <div class="d-flex align-items-center">
                    <div class="toggle-switch">
                      <input type="checkbox" id="enabledSwitch" v-model="roomData.isEnabled">
                      <label for="enabledSwitch" class="toggle-label" :class="{'toggle-enabled': roomData.isEnabled}">
                        <span class="toggle-inner"></span>
                      </label>
                    </div>
                    <span class="ms-2 small" :style="{ color: roomData.isEnabled ? '#28a745' : '#dc3545' }">
                      {{ roomData.isEnabled ? '會議室啟用' : '會議室停用' }}
                    </span>
                  </div>
                </div>
                <div class="card-body p-3">
                  <!-- 港別 -->
                  <div class="row">
                    <div class="col-3 mb-3">
                      <label class="form-label">港別 <span class="text-danger">*</span></label>
                      <select class="form-select" v-model="roomData.portId" @change="handlePortChange" :disabled="isEditMode"
                             :class="{'is-invalid': formErrors.portId}">
                        <option value="">請選擇</option>
                        <option v-for="port in portOptions" :key="port.portId" :value="port.portId">
                          {{ port.portName }}
                        </option>
                      </select>
                      <div class="invalid-feedback" v-if="formErrors.portId">{{ formErrors.portId }}</div>
                    </div>
                    <!-- 辦公室別 -->
                    <div class="col-6 mb-3">
                      <label class="form-label">辦公室別 <span class="text-danger">*</span></label>
                      <select class="form-select" v-model="roomData.officeId" @change="handleOfficeChange" :disabled="isEditMode"
                             :class="{'is-invalid': formErrors.officeId}">
                        <option value="">請選擇</option>
                        <option v-for="office in availableOffices" :key="office.officeId" :value="office.officeId">
                          {{ office.officeName }}
                        </option>
                      </select>
                      <div class="invalid-feedback" v-if="formErrors.officeId">{{ formErrors.officeId }}</div>
                    </div>
                    <!-- 會議室樓層 -->
                    <div class="col-3 mb-3">
                      <label class="form-label">會議室樓層 <span class="text-danger">*</span></label>
                      <select class="form-select" v-model="roomData.floor" :disabled="isEditMode"
                             :class="{'is-invalid': formErrors.floor}">
                        <option value="">請選擇</option>
                        <option v-for="floor in availableFloors" :key="floor.floor" :value="floor.floor">
                          {{ floor.floorName }}
                        </option>
                      </select>
                      <div class="invalid-feedback" v-if="formErrors.floor">{{ formErrors.floor }}</div>
                    </div>
                  </div>
                  
                  <!-- 會議室名稱和可容納人數 -->
                  <div class="row">
                    <div class="col-9 mb-3">
                      <label class="form-label">會議室名稱 <span class="text-danger">*</span></label>
                      <input type="text" class="form-control" v-model="roomData.roomName" placeholder="請輸入會議室名稱"
                             :class="{'is-invalid': formErrors.roomName}">
                      <div class="invalid-feedback" v-if="formErrors.roomName">{{ formErrors.roomName }}</div>
                    </div>
                    <div class="col-3 mb-3">
                      <label class="form-label">可容納人數 <span class="text-danger">*</span></label>
                      <input type="number" class="form-control" v-model="roomData.capacity" placeholder="請輸入可容納人數"
                             :class="{'is-invalid': formErrors.capacity}">
                      <div class="invalid-feedback" v-if="formErrors.capacity">{{ formErrors.capacity }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 右側：會議詳細設定 -->
            <div class="col-md-12">
              <div class="card">
                <div class="card-header">
                  <h6 class="card-title m-0 small">環控設備</h6>
                </div>
                <div class="card-body">
                  <div class="row p-3">
                      <div class="col-6 form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="videoSwitch"
                          v-model="roomData.hasVideoEquipment">
                        <label class="form-check-label" for="videoSwitch">是否有視訊設備</label>
                      </div>
                      <div class="col-6 form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="envSwitch"
                          v-model="roomData.hasEnvEquipment">
                        <label class="form-check-label" for="envSwitch">是否有環控設備</label>
                      </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="btn btn-secondary" @click="checkBeforeClose">取消</button>
        <button class="btn" :class="isEditMode ? 'btn-danger' : 'btn-success'" @click="saveRoom">
          {{ isEditMode ? '修改' : '新增' }}
        </button>
      </div>
    </div>
  </div>
  
  <!-- 添加確認對話框 -->
  <DiscardChangesModal 
    :visible="showDiscardChangesDialog" 
    message="是否捨棄尚未儲存的變更內容"
    @discard="discardChanges" 
    @keep="keepChanges"
    @update:visible="showDiscardChangesDialog = $event"
  />
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, reactive, computed, onMounted } from 'vue';
import DiscardChangesModal from './DiscardChangesModal.vue';
import { useToast } from 'vue-toastification';

const toast = useToast();

// 表單驗證狀態
const formErrors = reactive({
  portId: '',
  officeId: '',
  floor: '',
  roomName: '',
  capacity: ''
});

const props = defineProps({
  visible: Boolean,
  room: {
    type: Object,
    default: () => ({})
  },
  portOptions: {
    type: Array,
    default: () => []
  },
  officeOptions: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: '修改會議室'
  }
});

const emit = defineEmits(['update:visible', 'save', 'delete', 'close']);

// 判斷是否為編輯模式
const isEditMode = computed(() => {
  return !!props.room && !!props.room.id;
});

// 會議室資料
const roomData = reactive({
  id: '',
  portId: '',
  officeId: '',
  floor: '',
  roomName: '',
  capacity: 0,
  hasVideoEquipment: false,
  hasEnvEquipment: false,
  isEnabled: true
});

// 可用的辦公室選項
const availableOffices = ref([]);

// 可用的樓層選項
const availableFloors = ref([]);

// 添加變量來追踪表單是否被修改
const formChanged = ref(false);
// 添加變量來控制確認對話框的顯示
const showDiscardChangesDialog = ref(false);

// 監聽表單數據變化
watch(() => roomData, () => {
  if (props.visible) {
    formChanged.value = true;
  }
}, { deep: true });

// 當模態框打開時重置表單修改狀態和清空檢核結果
watch(() => props.visible, (isVisible) => {
  if (isVisible) {
    formChanged.value = false;
    // 清空所有表單驗證錯誤
    Object.keys(formErrors).forEach(key => {
      formErrors[key] = '';
    });
  }
});

// 檢查是否可以關閉模態框
function checkBeforeClose() {
  // 如果表單被修改過，顯示確認對話框
  if (formChanged.value) {
    showDiscardChangesDialog.value = true;
  } else {
    // 如果表單未修改，直接關閉
    close();
  }
}

// 處理確認捨棄變更
function discardChanges() {
  // 直接調用原始的 close 函數
  close();
  // 重置表單修改狀態
  formChanged.value = false;
}

// 處理取消捨棄變更
function keepChanges() {
  // 只需關閉確認對話框，不關閉主模態框
  showDiscardChangesDialog.value = false;
}

// 處理港別變更
const handlePortChange = () => {
  // 如果是編輯模式，不清空相關選項
  if (isEditMode.value) {
    // 只更新可用的辦公室選項，不清空已選擇的值
    if (!roomData.portId) return;
    
    console.log('處理港別變更 (編輯模式):', roomData.portId);
    
    // 從 portOptions 中找到對應的港別
    const selectedPort = props.portOptions.find(port => port.portId === roomData.portId);
    
    if (selectedPort && selectedPort.offices) {
      // 設置辦公室選項
      availableOffices.value = selectedPort.offices;
      console.log('設置可用的辦公室選項 (編輯模式):', availableOffices.value);
    }
    return;
  }
  
  // 非編輯模式，清空相關選項
  roomData.officeId = '';
  roomData.floor = '';
  availableOffices.value = [];
  availableFloors.value = [];
  
  if (!roomData.portId) return;
  
  console.log('處理港別變更:', roomData.portId);
  
  // 從 portOptions 中找到對應的港別
  const selectedPort = props.portOptions.find(port => port.portId === roomData.portId);
  
  if (selectedPort && selectedPort.offices) {
    // 設置辦公室選項
    availableOffices.value = selectedPort.offices;
    console.log('設置可用的辦公室選項:', availableOffices.value);
  }
};

// 處理辦公室變更
const handleOfficeChange = () => {
  // 如果是編輯模式，不清空相關選項
  if (isEditMode.value) {
    // 只更新可用的樓層選項，不清空已選擇的值
    if (!roomData.portId || !roomData.officeId) return;
    
    console.log('處理辦公室變更 (編輯模式):', roomData.officeId);
    
    // 從 portOptions 中找到對應的港別
    const selectedPort = props.portOptions.find(port => port.portId === roomData.portId);
    if (!selectedPort || !selectedPort.offices) return;
    
    // 從選中的港別中找到對應的辦公室
    const selectedOffice = selectedPort.offices.find(office => office.officeId === roomData.officeId);
    
    if (selectedOffice && selectedOffice.floors) {
      // 設置樓層選項
      availableFloors.value = selectedOffice.floors;
      console.log('設置可用的樓層選項 (編輯模式):', availableFloors.value);
    }
    return;
  }
  
  // 非編輯模式，清空相關選項
  roomData.floor = '';
  availableFloors.value = [];
  
  if (!roomData.portId || !roomData.officeId) return;
  
  console.log('處理辦公室變更:', roomData.officeId);
  
  // 從 portOptions 中找到對應的港別
  const selectedPort = props.portOptions.find(port => port.portId === roomData.portId);
  if (!selectedPort || !selectedPort.offices) return;
  
  // 從選中的港別中找到對應的辦公室
  const selectedOffice = selectedPort.offices.find(office => office.officeId === roomData.officeId);
  
  if (selectedOffice && selectedOffice.floors) {
    // 設置樓層選項
    availableFloors.value = selectedOffice.floors;
    console.log('設置可用的樓層選項:', availableFloors.value);
  }
};

// 監聽 visible 屬性變化，當模態視窗打開時初始化資料
watch(() => props.visible, (isVisible) => {
  if (isVisible && props.room) {
    initFormData();
  }
});

// 監聽 room 屬性變化，更新表單資料
watch(() => props.room, (newRoom) => {
  if (props.visible && newRoom && newRoom.id) {
    initFormData();
  }
}, { deep: true });

// 初始化表單資料
const initFormData = () => {
  if (!props.room) return;
  
  console.log('初始化表單資料:', props.room);
  
  // 清空表單數據
  Object.assign(roomData, {
    id: '',
    portId: '',
    officeId: '',
    floor: '',
    roomName: '',
    capacity: 0,
    hasVideoEquipment: false,
    hasEnvEquipment: false,
    isEnabled: true
  });
  
  // 清空所有表單驗證錯誤
  Object.keys(formErrors).forEach(key => {
    formErrors[key] = '';
  });
  
  // 如果是編輯模式，則填入現有數據
  if (props.room.id) {
    // 先設置港別，這樣可以觸發辦公室選項的載入
    roomData.portId = props.room.portId || '';
    
    // 手動調用港別變更處理函數，載入辦公室選項
    handlePortChange();
    
    // 設置辦公室ID
    roomData.officeId = props.room.officeId || '';
    
    // 手動調用辦公室變更處理函數，載入樓層選項
    handleOfficeChange();
    
    // 設置其他基本資訊
    roomData.id = props.room.id;
    roomData.floor = props.room.floor || '';
    roomData.roomName = props.room.roomName || '';
    roomData.capacity = props.room.capacity || 0;
    
    // 設置環控設備資訊
    roomData.hasVideoEquipment = props.room.onlineMeeting || false;
    roomData.hasEnvEquipment = props.room.envDevice || false;
    roomData.isEnabled = props.room.isEnabled !== undefined ? props.room.isEnabled : true;
  }
  
  console.log('初始化後的表單資料:', {
    portId: roomData.portId,
    officeId: roomData.officeId,
    floor: roomData.floor,
    availableOffices: availableOffices.value,
    availableFloors: availableFloors.value
  });
};

// 關閉模態框
const close = () => {
  emit('update:visible', false);
  emit('close');
};

// 保存會議室資料
const saveRoom = () => {
  // 驗證表單
  if (!validateForm()) {
    toast.error('請填寫所有必填欄位', {
      timeout: 3000
    });
    return;
  }
  
  // 轉換資料格式
  const roomToSave = {
    id: roomData.id,
    portId: roomData.portId,
    officeId: roomData.officeId,
    floor: roomData.floor,
    roomName: roomData.roomName,
    capacity: parseInt(roomData.capacity),
    onlineMeeting: roomData.hasVideoEquipment,
    envDevice: roomData.hasEnvEquipment,
    isEnabled: roomData.isEnabled
  };
  
  emit('save', roomToSave);
  // 保存後重置表單修改狀態
  formChanged.value = false;
};

// 驗證表單
function validateForm() {
  let isValid = true;
  
  // 重置所有錯誤訊息
  Object.keys(formErrors).forEach(key => {
    formErrors[key] = '';
  });
  
  // 驗證港別
  if (!roomData.portId) {
    formErrors.portId = '請選擇港別';
    isValid = false;
  }
  
  // 驗證辦公室別
  if (!roomData.officeId) {
    formErrors.officeId = '請選擇辦公室別';
    isValid = false;
  }
  
  // 驗證會議室樓層
  if (!roomData.floor) {
    formErrors.floor = '請選擇會議室樓層';
    isValid = false;
  }
  
  // 驗證會議室名稱
  if (!roomData.roomName.trim()) {
    formErrors.roomName = '請輸入會議室名稱';
    isValid = false;
  }
  
  // 驗證可容納人數
  if (!roomData.capacity || roomData.capacity <= 0) {
    formErrors.capacity = '請輸入有效的可容納人數';
    isValid = false;
  }
  
  return isValid;
}

// 刪除會議室
const deleteRoom = () => {
  emit('delete', roomData.id);
};

// 組件掛載時執行
onMounted(() => {
  // 如果模態視窗已經可見且有會議室資料，則初始化表單
  if (props.visible && props.room) {
    initFormData();
  }
});

// 監聽 visible 變化，當模態視窗打開時初始化表單
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.room) {
    initFormData();
  }
});
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
}

.modal-box {
  background: white;
  color: #333;
  border-radius: 10px;
  width: 80%;
  max-width: 650px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: modal-appear 0.3s forwards;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 500;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
}

.form-label {
  font-weight: 500;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 美化卡片樣式 */
.card {
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  padding: 0.75rem 1rem;
}

.card-body {
  padding: 1.25rem;
}

/* 美化表單元素 */
.form-control, .form-select {
  border-radius: 6px;
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 美化開關按鈕 */
.form-check-input {
  width: 2.5em;
  height: 1.25em;
  margin-top: 0.25em;
}

.form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.form-check-label {
  margin-left: 0.5rem;
}

/* 美化按鈕 */
.btn {
  border-radius: 6px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: white;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 美化警告提示 */
.alert {
  font-size: 0.875rem;
  border-radius: 6px;
  color: red;
}

/* 美化必填標記 */
.text-danger {
  color: #dc3545 !important;
}

/* 切換開關樣式 */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;  /* 從50px減小到40px */
  height: 20px; /* 從24px減小到20px */
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-label {
  position: relative;
  display: inline-block;
  width: 40px;  /* 從50px減小到40px */
  height: 20px; /* 從24px減小到20px */
  background-color: #dc3545; /* 紅色背景表示停用 */
  border-radius: 34px;
  cursor: pointer;
  transition: .4s;
  margin-bottom: 0;
}

.toggle-inner {
  position: absolute;
  content: "";
  height: 16px; /* 從18px減小到16px */
  width: 16px;  /* 從18px減小到16px */
  left: 2px;    /* 從3px減小到2px */
  bottom: 2px;  /* 從3px減小到2px */
  background-color: white;
  border-radius: 50%;
  transition: .4s;
}

input:checked + .toggle-label {
  background-color: #28a745; /* 綠色背景表示啟用 */
}

input:checked + .toggle-label .toggle-inner {
  transform: translateX(20px); /* 從26px減小到20px */
}

/* 確保標題中的文字垂直居中 */
.card-header .small {
  line-height: 1;
  font-size: 0.85rem; /* 稍微縮小文字大小 */
}
</style>
