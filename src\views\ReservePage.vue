<template>
    <MainLayout>
        <template #title>
            <span class="text-light fs-3 fw-bold">會議室預約</span>
        </template>
        <template #subTitle>
            <span class="text-white-50">提供快速、便利地預約會議室，即時查詢會議室使用情況</span>
        </template>
        <template #content> 
            <div>
                <!-- 樓層選擇區域 -->
                <div class="row mb-3">
                    <div class="col-1 d-flex align-items-center">
                        <span class="text-light fs-5 text-nowrap">樓&nbsp;&nbsp;&nbsp;&nbsp;層：</span>
                    </div>
                    <div class="col-11 d-flex align-items-center flex-wrap">
                        <button class="btn btn-sm rounded-pill btn-width"
                            :class="allSelectedFloor ? 'btn-primary' : 'btn-outline-light'" 
                            @click="toggleAllFloors">全選</button>
                        <button 
                            v-for="floor in floors" 
                            :key="floor.floorName"
                            class="btn btn-sm rounded-pill btn-width"
                            :class="floor.selected ? 'btn-primary' : 'btn-outline-light'" 
                            @click="toggleFloor(floor)">{{ floor.floorName }}</button>
                    </div>
                </div>
                
                <!-- 會議室選擇區域 -->
                <div class="row mb-3">
                    <div class="col-md-1 col-sm-4">
                        <span class="text-light fs-5 text-nowrap">會議室：</span>
                    </div>
                    <div class="col-11 d-flex align-items-center flex-wrap">
                        <button class="btn btn-sm rounded-pill btn-width"
                            :class="allSelectedRoom ? 'btn-primary' : 'btn-outline-light'" 
                            @click="toggleAllRooms">全選</button>
                        <template v-for="floor in selectedFloors" :key="floor.floorName">
                            <button 
                                v-for="room in floor.rooms" 
                                :key="room.id"
                                class="btn btn-sm rounded-pill btn-width"
                                :class="room.selected ? 'btn-primary' : 'btn-outline-light'" 
                                @click="toggleRoom(room)"
                                @mouseenter="showTooltip($event, `容納人數: ${room.capacity}`)"
                                @mouseleave="hideTooltip">{{ room.name }}</button>
                        </template>
                    </div>
                </div>
            </div>
            
            <!-- 日曆顯示區域 -->
            <div class="calendar-container">
                <CalendarView :selectedRooms="selectedRooms" :floors="floors"></CalendarView>
            </div>
            
            <!-- 自定義 tooltip -->
            <div class="custom-tooltip" ref="tooltip" v-show="tooltipVisible" :style="tooltipStyle">
                <div class="tooltip-inner" v-html="tooltipContent"></div>
            </div>
        </template>
    </MainLayout>
</template>
<style>
    .btn-width {
        width: 120px;
        margin-right: 10px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }
    
    /* 滑鼠經過未選中按鈕時的效果 */
    .btn-outline-light:hover {
        background-color: rgba(255, 255, 255, 0.3) !important;
        color: #fff !important;
        transform: scale(1.05);
        box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }
    
    /* 滑鼠經過已選中按鈕時的效果 */
    .btn-primary:hover {
        background-color: #D0BCFF !important; /* 更亮的紫色 */
        transform: scale(1.05);
        box-shadow: 0 0 10px rgba(234, 221, 255, 0.7);
    }
    
    .filter-container {
        position: sticky;
        top: 0;
        z-index: 100;
        background-color: #242424;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 0 0 10px 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    
    .calendar-container {
        margin-top: 20px;
    }
    
    /* 自定義 tooltip 樣式 */
    .custom-tooltip {
        position: fixed;
        z-index: 9999;
        transform: translateX(-50%) translateY(-100%);
        pointer-events: none;
    }

    .tooltip-inner {
        background-color: rgba(255, 255, 255, 0.9);
        color: black;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        max-width: 250px;
        min-width: 100px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .tooltip-inner::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: rgba(255, 255, 255, 0.9) transparent transparent transparent;
    }
</style>
<script setup>
import MainLayout from '../components/MainLayout.vue'
import CalendarView from '../components/CalendarView.vue'
import { computed, onMounted, ref, onBeforeMount, watch, reactive } from 'vue';
import { useRouter } from 'vue-router';
import api from '../utils/api';
import { useSystemLoadingStore } from '../store/SystemLoadingStore';

// Tooltip 相關變量
const tooltip = ref(null);
const tooltipVisible = ref(false);
const tooltipContent = ref('');
const tooltipStyle = reactive({
  left: '0px',
  top: '0px'
});

// 顯示 tooltip
const showTooltip = (event, content) => {
  if (!content) return;
  
  tooltipContent.value = content;
  
  // 計算位置
  const rect = event.currentTarget.getBoundingClientRect();
  tooltipStyle.left = `${rect.left + rect.width / 2}px`;
  tooltipStyle.top = `${rect.top - 10}px`;
  
  // 顯示 tooltip
  tooltipVisible.value = true;
};

// 隱藏 tooltip
const hideTooltip = () => {
  tooltipVisible.value = false;
};

// 路由實例
const router = useRouter();

// 系統載入狀態
const systemLoadingStore = useSystemLoadingStore();
const isSystemLoading = computed(() => systemLoadingStore.isLoading);

// 頁面載入狀態
const pageLoaded = ref(false);
const authChecked = ref(false);
const dataLoaded = ref(false);

// 檢查認證狀態
function checkAuthentication() {
    const token = localStorage.getItem('token');
    if (!token) {
        console.error('未找到認證令牌，重定向到登入頁面');
        router.push('/login');
        return false;
    }
    return true;
}

onBeforeMount(() => {
    // 在組件掛載前，先顯示載入狀態
    systemLoadingStore.showDiv();
    
    // 檢查認證狀態
    authChecked.value = checkAuthentication();
});

onMounted(async () => {
    console.log('ReservePage 組件已掛載');
    
    // 如果認證檢查失敗，不繼續執行
    if (!authChecked.value) {
        systemLoadingStore.hideDiv();
        return;
    }
    
    try {
        // 檢查是否是登入成功後的首次載入
        const loginSuccess = sessionStorage.getItem('loginSuccess') === 'true';
        if (loginSuccess) {
            console.log('檢測到登入成功標記，清除標記');
            sessionStorage.removeItem('loginSuccess');
        }
        
        // 載入會議室資料
        await loadRoomData();
        
        // 標記資料已載入
        dataLoaded.value = true;
        
        // 標記頁面已完全載入
        pageLoaded.value = true;
    } catch (error) {
        console.error('頁面載入過程中發生錯誤:', error);
    } finally {
        // 隱藏載入狀態
        systemLoadingStore.hideDiv();
    }
});

// 監視資料載入狀態
watch(dataLoaded, (newValue) => {
    if (newValue) {
        console.log('資料已成功載入，頁面準備就緒');
    }
});

// 監聽視窗大小變化，確保響應式佈局正確
window.addEventListener('resize', () => {
  if (pageLoaded.value) {
    // 強制重新計算佈局
    document.body.offsetHeight;
  }
});

// 資料狀態
const floors = ref([]);  // 所有樓層資料
const allSelectedFloor = ref(true);  // 是否全選樓層
const allSelectedRoom = ref(true);   // 是否全選會議室
const loadingRetries = ref(0);       // 載入重試次數

// 計算屬性：已選擇的樓層
const selectedFloors = computed(() => {
    return floors.value.filter(floor => floor.selected);
});

// 計算屬性：已選擇的會議室
const selectedRooms = computed(() => {
    return selectedFloors.value.flatMap(floor => 
        floor.rooms.filter(room => room.selected)
    );
});

// 計算屬性：檢查是否所有顯示的會議室都被選中
const checkAllRoomsSelected = computed(() => {
    if (selectedFloors.value.length === 0) return true;
    return selectedFloors.value.every(floor => 
        floor.rooms.every(room => room.selected)
    );
});

/**
 * 切換所有樓層的選擇狀態
 */
function toggleAllFloors() {
    allSelectedFloor.value = !allSelectedFloor.value;
    
    // 更新所有樓層及其會議室的選擇狀態
    floors.value.forEach(floor => {
        floor.selected = allSelectedFloor.value;
        floor.rooms.forEach(room => {
            room.selected = allSelectedFloor.value;
        });
    });
    
    // 同步更新會議室全選狀態
    allSelectedRoom.value = allSelectedFloor.value;
}

/**
 * 切換特定樓層的選擇狀態
 * @param {Object} floor - 樓層物件
 */
function toggleFloor(floor) {
    floor.selected = !floor.selected;
    
    // 更新該樓層下所有會議室的選擇狀態
    floor.rooms.forEach(room => {
        room.selected = floor.selected;
    });
    
    // 檢查是否所有樓層都被選中
    allSelectedFloor.value = floors.value.every(f => f.selected);
    
    // 更新會議室全選狀態
    allSelectedRoom.value = checkAllRoomsSelected.value;
}

/**
 * 切換所有會議室的選擇狀態
 */
function toggleAllRooms() {
    allSelectedRoom.value = !allSelectedRoom.value;
    
    // 更新所有會議室的選擇狀態
    floors.value.forEach(floor => {
        floor.rooms.forEach(room => {
            room.selected = allSelectedRoom.value;
        });
    });
    
    // 如果沒有選中任何會議室，則所有樓層也應該未選中
    //allSelectedFloor.value = allSelectedRoom.value && floors.value.every(floor => floor.selected);
}

/**
 * 切換特定會議室的選擇狀態
 * @param {Object} room - 會議室物件
 */
function toggleRoom(room) {
    room.selected = !room.selected;
    allSelectedRoom.value = checkAllRoomsSelected.value;
}

/**
 * 載入會議室資料
 */
async function loadRoomData() {
    try {
        await api.get('/api/reserve/rooms', {
            callback: function(response) {
                if (response && response.data) {
                    console.log('成功載入會議室資料:', response.data);
                    floors.value = response.data.floors || [];
                    
                    // 設置樓層和會議室的選擇狀態
                    floors.value.forEach(floor => {
                        floor.selected = true;
                        floor.rooms.forEach(room => {
                            room.selected = true;
                        });
                    });
                    
                    // 更新全選狀態
                    allSelectedFloor.value = true;
                    allSelectedRoom.value = true;
                } else {
                    console.error('載入會議室資料失敗: 回應無效');
                    retryLoadRoomData();
                }
            }
        });
    } catch (error) {
        console.error('載入會議室資料時發生錯誤:', error);
        retryLoadRoomData();
    }
}

/**
 * 重試載入會議室資料
 */
function retryLoadRoomData() {
    if (loadingRetries.value < 3) {
        loadingRetries.value++;
        console.log(`重試載入會議室資料 (${loadingRetries.value}/3)...`);
        setTimeout(loadRoomData, 1000);
    } else {
        console.error('載入會議室資料失敗，已達最大重試次數');
    }
}

</script>
