<template>
  <div v-if="visible" class="discard-changes-overlay">
    <div class="discard-changes-dialog">
      <div class="discard-changes-header">
        <div class="discard-changes-icon">
          <i class="bi bi-exclamation-triangle-fill"></i>
        </div>
      </div>
      <div class="discard-changes-content">
        <p class="discard-changes-message" v-html="message"></p>
      </div>
      <div class="discard-changes-buttons">
        <button class="btn btn-outline-secondary" @click="onCancel">取消</button>
        <button class="btn btn-danger" @click="onConfirm">確定</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  message: {
    type: String,
    default: '是否捨棄尚未儲存的變更內容'
  }
});

const emit = defineEmits(['update:visible', 'discard', 'keep']);

function onConfirm() {
  emit('discard');
  emit('update:visible', false);
}

function onCancel() {
  emit('keep');
  emit('update:visible', false);
}
</script>

<style scoped>
.discard-changes-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100; /* 確保在其他模態框之上 */
}

.discard-changes-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  width: 360px;
  overflow: hidden;
  animation: dialog-appear 0.3s forwards;
  color: #333;
}

.discard-changes-header {
  padding: 20px 20px 0;
  display: flex;
  justify-content: center;
}

.discard-changes-icon {
  font-size: 48px;
  color: #f0ad4e;
  margin-bottom: 10px;
}

.discard-changes-content {
  padding: 10px 20px 20px;
  text-align: center;
}

.discard-changes-message {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.5;
}

.discard-changes-buttons {
  display: flex;
  justify-content: flex-end; /* 改為右側對齊 */
  padding: 16px 20px 20px; /* 增加底部內邊距 */
  gap: 12px; /* 減少按鈕間距 */
  margin-top: 10px; /* 與上方內容增加間距 */
}

.discard-changes-buttons button {
  min-width: 80px;
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 500;
}

.btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  color: white;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

@keyframes dialog-appear {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 添加響應式設計 */
@media (max-width: 480px) {
  .discard-changes-dialog {
    width: 90%;
    max-width: 360px;
  }
  
  .discard-changes-content {
    padding: 10px 16px 20px;
  }
  
  .discard-changes-buttons {
    padding: 12px 16px 16px;
  }
  
  .discard-changes-buttons button {
    min-width: 70px;
    padding: 8px 16px;
  }
}
</style>
