<script setup>
    import { computed, onMounted, ref } from 'vue';
    import { useRoute, useRouter } from 'vue-router';
    import { useSystemLoadingStore } from '../store/SystemLoadingStore';

    // 獲取當前路徑，用於高亮顯示當前選單項
    const route = useRoute();
    const currentPath = computed(() => route.path);
    const router = useRouter();

    // 從環境變數中獲取基礎URL
    const baseUrl = import.meta.env.BASE_URL;

    const systemLoadingStore = useSystemLoadingStore()
    const isLoading = computed(() => systemLoadingStore.isLoading)

    // 從 localStorage 獲取用戶資訊
    const userInfo = computed(() => {
        try {
            const userInfoStr = localStorage.getItem('userInfo');
            return userInfoStr ? JSON.parse(userInfoStr) : null;
        } catch (e) {
            console.error('解析用戶資訊時出錯:', e);
            return null;
        }
    });
    
    // 計算用戶是否為管理員
    const isAdmin = computed(() => {
        return userInfo.value && userInfo.value.role === 'ROLE_ADMIN';
    });
    
    // 獲取用戶全名
    const fullName = computed(() => userInfo.value?.fullName || '');
    const companyName = computed(() => userInfo.value?.companyName + " / " + userInfo.value?.departmentName || '');
    
    // 追蹤佈局是否已準備就緒
    const layoutReady = ref(false);

    const handleLogout = async () => {
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        sessionStorage.removeItem('tableTheme');
        router.push('/login');
    }
    
    // 確保佈局正確載入
    onMounted(async () => {
        // 等待一小段時間確保DOM已完全渲染
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // 強制重新計算佈局
        document.body.offsetHeight;
        
        // 標記佈局已準備就緒
        layoutReady.value = true;
    });
</script>
<template>
    <div class="layoutBackGround row">
        <!-- 左側選單 -->
        <div class="col-2">
            <div class="menu_Content">
                <img class="menu_logo" src="/src/assets/images/menu-logo.png" alt=""/>
                <div name="menuItem">
                    <a class="menuBtn" :class="{ 'menuBtn-active': currentPath === '/reserve' }" :href="`${baseUrl}reserve`">
                        <img class="menuBtn_img" src="/src/assets/images/menuBook.svg" alt="" />
                        <span class="menuBtn_txt">會議室預約</span>
                    </a>
                </div>
                <div name="menuItem">
                    <a class="menuBtn" :class="{ 'menuBtn-active': currentPath === '/record' }" :href="`${baseUrl}record`">
                        <img class="menuBtn_img" src="/src/assets/images/menuCalendar.svg" alt="" />
                        <span class="menuBtn_txt">會議查詢</span>
                    </a>
                </div>
                <div name="menuItem" v-if="isAdmin">
                    <a class="menuBtn" :class="{ 'menuBtn-active': currentPath === '/statistics' }" :href="`${baseUrl}statistics`">
                        <img class="menuBtn_img" src="/src/assets/images/chartLine.png" alt="" />
                        <span class="menuBtn_txt">統計報表</span>
                    </a>
                </div>
                <div name="menuItem" v-if="isAdmin">
                    <a class="menuBtn" :class="{ 'menuBtn-active': currentPath === '/manage' }" :href="`${baseUrl}manage`">
                        <img class="menuBtn_img" src="/src/assets/images/menuManage.svg" alt="" />
                        <span class="menuBtn_txt">會議室管理</span>
                    </a>
                </div>
                
                <!-- 左下角登出區域 -->
                <div class="logout-container">
                    <div class="user-info">
                        <div class="user-avatar">
                            <span>{{ fullName.charAt(0) }}</span>
                        </div>
                        <div class="user-details">
                            <div class="user-name">{{ fullName }}</div>
                            <div class="user-department">{{ companyName }}</div>
                        </div>
                        <button class="logout-btn" @click="handleLogout" title="登出系統">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右側內容區 -->
        <div class="col-10 content-area">
            <!-- 頁面標題區 -->
            <div class="page-header">
                <div class="title-wrapper">
                    <slot name="title"></slot>
                </div>
                <div class="subtitle-wrapper">
                    <slot name="subTitle"></slot>
                </div>
            </div>
            
            <!-- 主要內容區 -->
            <div class="main-content p-3 pt-0">
                <slot name="content"></slot>
            </div>
        </div>
        
        <!-- 載入指示器 -->
        <div v-show="isLoading" class="loading-overlay">
            <div class="fullscreen-overlay"></div>
            <div class="loader text-center">
                <span style="font-size: 72pt;">Loading</span><br>載入中...
            </div>
        </div>
    </div>
</template>

<style scoped>
/* 頁面標題區域樣式 */
.page-header {
    padding: 20px 0;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative; /* 添加相對定位，作為登出區域的參考點 */
}

.title-wrapper {
    margin-bottom: 10px;
    font-size: 24px;
    font-weight: bold;
}

.subtitle-wrapper {
    font-size: 16px;
    color: #aaa;
}

/* 登出區域樣式 - 左下角 */
.logout-container {
    margin-top: auto;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #2c3e50, #1a2533);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    padding: 12px;
    transition: all 0.3s ease;
    width: 200px;
    position: fixed;
    bottom: 20px;
    left: 20px;
}

.logout-container:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366F1, #8B5CF6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.4);
    flex-shrink: 0;
}

.user-details {
    flex: 1;
    overflow: hidden;
}

.user-name {
    color: white;
    font-weight: 600;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-department {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: 2px;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 8px;
    color: white;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;
    padding: 0;
    flex-shrink: 0;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.logout-btn i {
    font-size: 14px;
}

/* 確保選單內容區域有足夠的空間容納登出區域 */
.menu_Content {
    position: fixed;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    padding: 50px 0 0 20px;
    height: 100vh;
}
</style>
