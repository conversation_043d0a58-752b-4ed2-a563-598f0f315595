<template>
    <MainLayout>
        <template #title>
            <span class="text-light" style="font-size: 32px;">統計報表</span>
        </template>
        <template #subTitle>
            <span class="text-light" style="font-size: 15px;">檢視會議室使用資訊</span>
        </template>
        <template #content>
            
            <!-- 儀表板區域 -->
            <div class="dashboard-section mb-4">
                <div class="row">
                    <!-- 儀表板1：預約記錄圓餅圖 -->
                    <div class="col-md-2 mb-4">
                        <div class="card bg-transparent border-light rounded-4 h-100">
                            <div class="card-header bg-transparent border-light">
                                <h5 class="card-title text-light mb-0 small text-center">
                                    <span id="currentMonth">6</span>月份預約記錄
                                </h5>
                            </div>
                            <div class="card-body d-flex justify-content-center align-items-center">
                                <div class="chart-container" style="position: relative; height:150px; width:150px">
                                    <!-- 圓餅圖將在這裡渲染 -->
                                    <canvas id="pieChart"></canvas>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent border-light">
                                <div class="d-flex justify-content-center">
                                    <div class="legend-item me-4">
                                        <span class="legend-color" style="background-color: #7C3AED;"></span>
                                        <span class="legend-text text-light">預約</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-color" style="background-color: #06B6D4;"></span>
                                        <span class="legend-text text-light">取消</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 儀表板2：預約數量折線圖 -->
                    <div class="col-md-5 mb-4">
                        <div class="card bg-transparent border-light rounded-4 h-100">
                            <div class="card-header bg-transparent border-light">
                                <h5 class="card-title text-light mb-0 small text-center">
                                    <span id="currentMonth2">6</span>月份預約次數統計
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="position: relative; height:200px;">
                                    <!-- 折線圖將在這裡渲染 -->
                                    <canvas id="lineChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 儀表板3：會議室使用次數長條圖 -->
                    <div class="col-md-5 mb-4">
                        <div class="card bg-transparent border-light rounded-4 h-100">
                            <div class="card-header bg-transparent border-light">
                                <h5 class="card-title text-light mb-0 small text-center">
                                    <span id="currentMonth3">6</span>月份會議室租借次數統計
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="position: relative; height:200px;">
                                    <!-- 長條圖將在這裡渲染 -->
                                    <canvas id="barChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- 篩選條件區域 -->
            <div class="filter-section mb-4">
                <div class="card bg-transparent border-light rounded-4 p-3">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <label for="roomSelect" class="form-label text-light">會議室</label>
                            <select id="roomSelect" class="form-select dark-input" v-model="selectedRoom">
                                <option value="">請選擇</option>
                                <option v-for="room in roomOptions" :key="room.id" :value="room.id">{{ room.roomName }}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="startDate" class="form-label text-light">開始日期</label>
                            <DatePicker id="startDate" v-model="startDate" input-class="dark-input" placeholder="選擇日期" />
                        </div>
                        <div class="col-md-3">
                            <label for="endDate" class="form-label text-light">結束日期</label>
                            <DatePicker id="endDate" v-model="endDate" input-class="dark-input" placeholder="選擇日期" />
                        </div>
                        <div class="col-md-3 d-flex justify-content-end">
                            <button class="btn btn-outline-light rounded-pill px-4" @click="clearSearch">清除</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 查詢結果表格 -->
            <SearchResultTable 
                ref="searchTableRef"
                :columns="columns" 
                initial-sort-field="reserveDateStart" 
                initial-sort-direction="asc" 
                api-url="/api/statistics/search" 
                v-model:isDarkMode="isDarkMode"
                :skip-initial-fetch="true"
                :loading="true"
                :search-params="{
                    roomId: selectedRoom === 'all' ? '' : selectedRoom,
                    startDate: startDate,
                    endDate: endDate,
                    searchText: searchText
                }"
                @data-loaded="handleDataLoaded">
                <template #headerArea>
                    <h5 class="mb-0" :class="{'text-light': isDarkMode, 'text-dark': !isDarkMode}" style="display: flex; align-items: center;gap: 20px;">會議室使用記錄
                        <input 
                            type="text" 
                            class="form-control search-input" 
                            style="width: 300px;" 
                            id="searchInput" 
                            v-model="searchText" 
                            placeholder="請輸入關鍵字搜尋"
                        >
                        <button 
                            class="btn btn-success rounded-pill px-4" 
                            @click="exportReport"
                            title="匯出報表"
                        >
                            <i class="fa fa-download me-1"></i> 匯出報表
                        </button>
                    </h5>
                </template>
            </SearchResultTable>
        </template>
    </MainLayout>
</template>

<script setup>
import MainLayout from '../components/MainLayout.vue';
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import Chart from 'chart.js/auto';
import api from '../utils/api';
import { getCurrentTaiwanMonth, formatToRocDate } from '../utils/dateUtils';
import SearchResultTable from '../components/common/SearchResultTable.vue';
import DatePicker from '../components/common/DatePicker.vue';
import { useSystemLoadingStore } from '../store/SystemLoadingStore';
const systemLoadingStore = useSystemLoadingStore();

const isDarkMode = ref(true);
const startDate = ref('');
const endDate = ref('');
const roomOptions = ref([]);
const selectedRoom = ref('');
const currentMonth = ref('');
const searchTableRef = ref(null);
const isInitialLoad = ref(true); // 添加初始載入標記
const searchText = ref(''); // 添加搜尋文字變數
const searchTimeout = ref(null); // 添加防抖定時器
// 添加一個標記，表示是否正在手動觸發搜索
const isManualSearch = ref(false);

// 添加數據加載完成的處理函數
const handleDataLoaded = (data) => {
    console.log('數據加載完成:', data);
    // 確保隱藏全局載入指示器
    systemLoadingStore.hideDiv();
};

// 定義圓餅圖資料 - 使用更鮮明的對比色
const pieChartData = ref({
    labels: ['預約', '取消'],
    datasets: [{
        data: [0, 0], // 初始化為 0
        backgroundColor: ['#7C3AED', '#06B6D4'], // 更新顏色：亮紫色和亮青色
        borderWidth: 1,
        borderColor: '#2D3748', // 深色邊框增加對比度
        hoverBackgroundColor: ['#8B5CF6', '#22D3EE'] // 懸停時顏色更亮
    }]
});

// 定義折線圖資料 - 使用漸變色
const lineChartData = ref({
    labels: [], // 日期標籤
    datasets: [{
        label: '預約次數',
        data: [], // 初始化為空陣列
        borderColor: '#6366F1', // 靛藍色線條
        backgroundColor: (context) => {
            const chart = context.chart;
            const {ctx, chartArea} = chart;
            if (!chartArea) return null;
            // 創建漸變背景
            const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
            gradient.addColorStop(0, 'rgba(99, 102, 241, 0)');
            gradient.addColorStop(1, 'rgba(99, 102, 241, 0.3)');
            return gradient;
        },
        tension: 0.4, // 增加曲線平滑度
        fill: true
    }]
});

// 定義長條圖資料 - 使用多種顏色
const barChartData = ref({
    labels: [], // 會議室名稱
    datasets: [{
        label: '租借次數',
        data: [], // 初始化為空陣列
        backgroundColor: [], // 將在資料載入後動態設定
        borderColor: [], // 將在資料載入後動態設定
        borderWidth: 1,
        borderRadius: 6 // 圓角柱狀
    }]
});

// 定義當前月份
const currentDate = ref(new Date()); // 當前年

// 獲取統計資料
const fetchStatisticsData = async () => {
    try {
        await api.post('/api/statistics/record', {
            month: getCurrentTaiwanMonth()
        }, {
            callback: function(response) {
                console.log('統計資料回應:', response.data);
                
                // 處理圓餅圖資料
                if (response.data && response.data.pieChart) {
                    pieChartData.value.datasets[0].data = [
                        response.data.pieChart.reserved || 0,
                        response.data.pieChart.canceled || 0
                    ];
                    
                    // 重新渲染圓餅圖
                    renderPieChart();
                }
                
                // 處理折線圖資料
                if (response.data && response.data.lineChart) {
                    // 假設 API 返回的格式是 {date: '2023-06-01', count: 5}
                    const lineData = response.data.lineChart;
                    
                    // 提取日期和預約次數
                    lineChartData.value.labels = lineData.map(item => item.date);
                    lineChartData.value.datasets[0].data = lineData.map(item => item.count);
                    
                    // 重新渲染折線圖
                    renderLineChart();
                }
                
                // 處理長條圖資料
                if (response.data && response.data.barChart) {
                    // 假設 API 返回的格式是 {room: 'T1102 會議室', count: 15}
                    const barData = response.data.barChart;
                    
                    // 提取會議室名稱和租借次數
                    barChartData.value.labels = barData.map(item => item.room);
                    barChartData.value.datasets[0].data = barData.map(item => item.count);
                    
                    // 設定不同的顏色
                    const colors = [
                        '#10B981', // 綠色
                        '#3B82F6', // 藍色
                        '#8B5CF6', // 紫色
                        '#EC4899', // 粉色
                        '#F59E0B', // 橙色
                        '#EF4444', // 紅色
                        '#06B6D4', // 青色
                        '#6366F1'  // 靛藍色
                    ];
                    
                    // 為每個會議室分配顏色
                    barChartData.value.datasets[0].backgroundColor = barData.map((_, index) => {
                        const colorIndex = index % colors.length;
                        return colors[colorIndex];
                    });
                    
                    // 設定相同的邊框顏色
                    barChartData.value.datasets[0].borderColor = barData.map((_, index) => {
                        const colorIndex = index % colors.length;
                        return colors[colorIndex];
                    });
                    
                    // 重新渲染長條圖
                    renderBarChart();
                }
                
                // 更新月份顯示
                if (response.data && response.data.month) {
                    currentMonth.value = response.data.month;
                    // 更新所有月份顯示
                    document.getElementById('currentMonth').textContent = currentMonth.value;
                    document.getElementById('currentMonth2').textContent = currentMonth.value;
                    document.getElementById('currentMonth3').textContent = currentMonth.value;
                }
            },
            error: function(error) {
                console.error('獲取統計資料失敗:', error);
            }
        });
    } catch (error) {
        console.error('獲取統計資料時發生錯誤:', error);
    }
};

// 渲染圓餅圖
let pieChart = null;
const renderPieChart = () => {
    const pieCtx = document.getElementById('pieChart').getContext('2d');
    
    // 如果圖表已存在，先銷毀
    if (pieChart) {
        pieChart.destroy();
    }
    
    // 創建新圖表
    pieChart = new Chart(pieCtx, {
        type: 'doughnut', // 使用環形圖而非圓餅圖，中間有空白
        data: pieChartData.value,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    },
                    backgroundColor: 'rgba(17, 24, 39, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1,
                    padding: 10,
                    cornerRadius: 6
                }
            },
            cutout: '70%', // 增加中間空白區域
            animation: {
                animateScale: true,
                animateRotate: true
            }
        },
        plugins: [{
            id: 'pieChartLabels',
            afterDraw: function(chart) {
                const ctx = chart.ctx;
                chart.data.datasets.forEach((dataset, i) => {
                    const meta = chart.getDatasetMeta(i);
                    if (!meta.hidden) {
                        // 計算總數
                        const total = dataset.data.reduce((a, b) => a + b, 0);
                        
                        // 繪製中間的總數
                        ctx.save();
                        ctx.fillStyle = '#fff';
                        ctx.font = 'bold 16px Arial';
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';
                        
                        // 獲取圖表中心位置
                        const centerX = (chart.chartArea.left + chart.chartArea.right) / 2;
                        const centerY = (chart.chartArea.top + chart.chartArea.bottom) / 2;
                        
                        // 繪製總數
                        ctx.fillText(`${total}`, centerX, centerY - 10);
                        
                        // 繪製"總數"文字
                        ctx.font = '12px Arial';
                        ctx.fillText('總數', centerX, centerY + 10);
                        ctx.restore();
                        
                        // 繪製各部分的數值和百分比
                        meta.data.forEach((element, index) => {
                            // 獲取數據
                            const data = dataset.data[index];
                            const percentage = total > 0 ? Math.round((data / total) * 100) : 0;
                            
                            // 獲取位置
                            const position = element.tooltipPosition();
                            
                            // 繪製文字
                            ctx.fillStyle = '#fff';
                            ctx.font = 'bold 12px Arial';
                            ctx.textAlign = 'center';
                            ctx.textBaseline = 'middle';
                            
                            // 計算文字位置（在扇形區域內）
                            const angle = element.startAngle + (element.endAngle - element.startAngle) / 2;
                            const radius = element.outerRadius * 0.85; // 在半徑的85%處顯示
                            
                            // 計算文字位置
                            const textX = centerX + Math.cos(angle) * radius;
                            const textY = centerY + Math.sin(angle) * radius;
                            
                            // 顯示百分比
                            ctx.fillText(`${percentage}%`, textX, textY);
                        });
                    }
                });
            }
        }]
    });
};

// 渲染折線圖
let lineChart = null;
const renderLineChart = () => {
    const lineCtx = document.getElementById('lineChart').getContext('2d');
    
    // 如果圖表已存在，先銷毀
    if (lineChart) {
        lineChart.destroy();
    }
    
    // 創建新圖表
    lineChart = new Chart(lineCtx, {
        type: 'line',
        data: lineChartData.value,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: '#fff',
                        font: {
                            size: 10
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)',
                        drawBorder: false
                    }
                },
                x: {
                    ticks: {
                        color: '#fff',
                        font: {
                            size: 10
                        }
                    },
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(17, 24, 39, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1,
                    padding: 10,
                    cornerRadius: 6,
                    displayColors: false
                }
            },
            elements: {
                point: {
                    radius: 3,
                    hoverRadius: 5,
                    backgroundColor: '#6366F1',
                    borderColor: '#fff',
                    borderWidth: 2
                },
                line: {
                    tension: 0.4
                }
            },
            interaction: {
                mode: 'index',
                intersect: false
            },
            animation: {
                duration: 1000
            }
        }
    });
};

// 渲染長條圖
let barChart = null;
const renderBarChart = () => {
    const barCtx = document.getElementById('barChart').getContext('2d');
    
    // 如果圖表已存在，先銷毀
    if (barChart) {
        barChart.destroy();
    }
    
    // 創建新圖表
    barChart = new Chart(barCtx, {
        type: 'bar',
        data: barChartData.value,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: '#fff',
                        font: {
                            size: 10
                        }
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)',
                        drawBorder: false
                    }
                },
                x: {
                    ticks: {
                        color: '#fff',
                        font: {
                            size: 10
                        }
                    },
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(17, 24, 39, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: 'rgba(255, 255, 255, 0.1)',
                    borderWidth: 1,
                    padding: 10,
                    cornerRadius: 6,
                    displayColors: false
                }
            },
            animation: {
                duration: 1000
            },
            barPercentage: 0.7, // 調整柱狀圖寬度
            categoryPercentage: 0.8
        }
    });
};

// 更新圖例顏色
const updateLegendColors = () => {
    // 更新圖例顏色
    const legendItems = document.querySelectorAll('.legend-color');
    if (legendItems.length >= 2) {
        legendItems[0].style.backgroundColor = '#7C3AED'; // 預約顏色
        legendItems[1].style.backgroundColor = '#06B6D4'; // 取消顏色
    }
};

const columns = [
    { title: '會議主題', field: 'subject', sortable: true, width: '150px' },
    { title: '會議日期', field: 'reserveDateStart', sortable: true, width: '120px', render: (data) => {
        return formatToRocDate(data.reserveDate);
    } },
    { title: '持續時間', field: 'duration', sortable: false, width: '120px'},
    { title: '會議室', field: 'roomName', sortable: true, width: '120px' },
    { title: '預約單位', field: 'departmentName', sortable: true, width: '120px' },
    { title: '聯絡人', field: 'contactName', sortable: true, width: '120px' },    
    { title: '主持人', field: 'hostName', sortable: true, width: '120px' },
    { title: '是否公開', field: 'isPublic', sortable: true, width: '120px', render: (data) => {
        return data.isPublic ? '<span class="text-success">公開</span>' : '不公開';
    } },
    { title: '狀態', field: 'status', sortable: true, width: '120px' },
    { title: '預約人數', field: 'attendees', sortable: true, width: '120px' }
];

// 載入會議室選項
const loadRoomOptions = async () => {
    try {
        await api.get('/api/statistics/rooms', {
            callback: function(response) {
                console.log('會議室選項 API 回應:', response);
                if (response.data && Array.isArray(response.data)) {
                    roomOptions.value = response.data;
                    console.log('成功載入會議室選項:', roomOptions.value);
                } else {
                    console.error('載入會議室選項失敗: 回應格式不正確');
                }
            },
            error: function(error) {
                console.error('載入會議室選項時發生錯誤:', error);
            }
        });
    } catch (error) {
        console.error('載入會議室選項時發生錯誤:', error);
    }
};

// 查詢統計資料
const searchStatistics = () => {
    // 如果有表格引用，調用其刷新方法
    if (searchTableRef.value) {
        console.log('執行查詢，參數:', {
            roomId: selectedRoom.value,
            startDate: startDate.value,
            endDate: endDate.value,
            searchText: searchText.value
        });
        
        // 設置手動搜索標記為 true
        isManualSearch.value = true;
        
        // 更新搜尋參數
        const params = {
            roomId: selectedRoom.value === 'all' ? '' : selectedRoom.value,
            startDate: startDate.value,
            endDate: endDate.value,
            searchText: searchText.value
        };
        
        // 使用單一操作更新參數並執行查詢
        nextTick(() => {
            searchTableRef.value.updateSearchParams(params);
            // 直接調用 fetchData，不通過監聽器
            searchTableRef.value.fetchData();
            
            // 延遲重置手動搜索標記
            setTimeout(() => {
                isManualSearch.value = false;
            }, 100);
        });
    }
};

// 添加防抖處理的搜尋函數
const debouncedSearch = () => {
    // 如果是手動搜索，不執行自動搜索
    if (isManualSearch.value) return;
    
    // 清除之前的定時器
    if (searchTimeout.value) {
        clearTimeout(searchTimeout.value);
    }
    
    // 設置新的定時器，延遲 300ms 執行查詢
    searchTimeout.value = setTimeout(() => {
        searchStatistics();
    }, 300);
};

// 清除查詢條件
const clearSearch = () => {
    selectedRoom.value = '';
    startDate.value = '';
    endDate.value = '';
    searchText.value = '';
    // 清除後自動刷新查詢結果
    searchStatistics();
};

// 監聽選項變更，使用防抖處理
watch(() => selectedRoom.value, (newVal, oldVal) => {
    if (!isInitialLoad.value) {
        debouncedSearch();
    }
});

watch(() => startDate.value, (newVal, oldVal) => {
    if (!isInitialLoad.value) {
        debouncedSearch();
    }
});

watch(() => endDate.value, (newVal, oldVal) => {
    if (!isInitialLoad.value) {
        debouncedSearch();
    }
});

watch(() => searchText.value, (newVal, oldVal) => {
    if (!isInitialLoad.value) {
        debouncedSearch();
    }
});

// 匯出報表函數
const exportReport = async () => {
    try {
        systemLoadingStore.showDiv(); // 顯示載入中提示
        
        // 獲取當前表格的分頁和排序參數
        const currentPage = searchTableRef.value ? searchTableRef.value.getCurrentPage() : 1;
        const pageSize = searchTableRef.value ? searchTableRef.value.getPageSize() : 10;
        const sortInfo = searchTableRef.value ? searchTableRef.value.getSortInfo() : { field: 'subject', direction: 'asc' };
        
        // 準備匯出參數
        const params = {
            roomId: selectedRoom.value === 'all' ? '' : selectedRoom.value,
            startDate: startDate.value,
            endDate: endDate.value,
            searchText: searchText.value,
            page: currentPage,
            limit: pageSize,
            sorts: [sortInfo]
        };
        
        // 呼叫匯出 API
        await api.post('/api/statistics/export', params, {
            responseType: 'blob', // 設定回應類型為 blob
            callback: function(response) {
                if(response.status === 200) {   
                    console.log('匯出報表成功:', response.data);
                    // 創建 Blob 對象
                    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' });
                    
                    // 創建下載連結
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    
                    // 設定檔案名稱
                    const fileName = `會議室使用統計_${new Date().toISOString().slice(0, 10)}.xlsx`;
                    link.download = fileName;
                    
                    // 觸發下載
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    // 隱藏載入中提示
                    systemLoadingStore.hideDiv();
                    return;
                } else {
                    console.error('匯出報表失敗: 狀態碼', response.status);
                    systemLoadingStore.hideDiv();
                    return;
                } 
            }
        });
    } catch (error) {
        console.error('匯出報表時發生錯誤:', error);
        systemLoadingStore.hideDiv();
    }
};

// 在組件掛載後獲取資料並初始化圖表
onMounted(async () => {
    // 獲取統計資料
    await fetchStatisticsData();
    
    // 載入會議室選項
    await loadRoomOptions();
    
    // 更新圖例顏色
    setTimeout(updateLegendColors, 500);
    
    // 初始載入完成後，設置標記為 false
    setTimeout(() => {
        isInitialLoad.value = false;
        // 初始化完成後執行一次查詢
        searchStatistics();
    }, 1000);
});
</script>

<style>
.filter-section, .dashboard-section, .table-section {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.dark-input {
    background-color: #343a40;
    border-color: rgba(255, 255, 255, 0.175);
    color: #fff;
}

.dark-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 0 8px;
}

.legend-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 3px;
    margin-right: 5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.legend-item:first-child .legend-color {
    background-color: #7C3AED; /* 預約顏色 - 亮紫色 */
}

.legend-item:last-child .legend-color {
    background-color: #06B6D4; /* 取消顏色 - 亮青色 */
}

.chart-container {
    margin: 0 auto;
    position: relative;
    transition: all 0.3s ease;
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

/* 搜尋輸入欄位樣式 */
.search-input {
    background-color: #343a40 !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
    font-weight: 500 !important;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
    opacity: 1 !important;
}

/* 淺色模式下的搜尋輸入欄位 */
.light-card .search-input {
    background-color: #ffffff !important;
    border-color: rgba(0, 0, 0, 0.2) !important;
    color: #212529 !important;
}

.light-card .search-input::placeholder {
    color: rgba(0, 0, 0, 0.5) !important;
}

/* 增強輸入欄位的焦點效果 */
.search-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
}

.light-card .search-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
    border-color: #86b7fe !important;
}
</style>
