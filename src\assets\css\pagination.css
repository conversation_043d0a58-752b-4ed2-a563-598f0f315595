/* 分頁條樣式 */
.pagination {
    margin-bottom: 20px;
}

.pagination .page-link {
    color: #f8f9fa;
    background-color: #343a40;
    border-color: #495057;
}

.pagination .page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    background-color: #343a40;
    border-color: #495057;
}

/* 淺色模式下的分頁條樣式 */
.light-card .pagination .page-link {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.light-card .pagination .page-item.active .page-link {
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.light-card .pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* 分頁條懸停效果 */
.pagination .page-link:hover {
    z-index: 2;
    color: #fff;
    background-color: #495057;
    border-color: #6c757d;
    text-decoration: none;
}

.light-card .pagination .page-link:hover {
    color: #0d6efd;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* 確保分頁條在各種主題下都能正確顯示 */
.pagination .page-item:first-child .page-link {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.pagination .page-item:last-child .page-link {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

/* 修正每頁筆數選單樣式 */
.page-size-select {
    width: 90px;
    background-color: #343a40;
    color: #f8f9fa;
    border-color: #495057;
    appearance: auto; /* 確保下拉箭頭顯示 */
    -webkit-appearance: auto; /* Safari 支持 */
    -moz-appearance: auto; /* Firefox 支持 */
    padding-right: 25px; /* 為下拉箭頭留出空間 */
}

.page-size-select:focus {
    border-color: #6c757d;
    box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.25);
}

/* 淺色模式下的每頁筆數選單 */
.light-card .page-size-select {
    background-color: #f8f9fa;
    color: #212529;
    border-color: #dee2e6;
}

/* 添加排序相關樣式，保持與現有樣式兼容 */
.table th {
    cursor: pointer;
    position: relative;
    vertical-align: middle;
    height: 45px;
}

.table th:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.light-table .table th:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* 排序圖標樣式 */
.fa-sort, .fa-sort-up, .fa-sort-down {
    font-size: 0.75rem;
    margin-left: 4px;
    opacity: 0.6;
    vertical-align: middle;
}

.table th:hover .fa-sort {
    opacity: 0.9;
}

/* 激活的排序圖標 */
.fa-sort-up, .fa-sort-down {
    opacity: 1;
    color: #fff;
}

.light-table .fa-sort-up, 
.light-table .fa-sort-down {
    color: #212529;
}

/* 不可排序的列 */
.table th:nth-child(9),
.table th:last-child {
    cursor: default;
}

.table th:nth-child(9):hover,
.table th:last-child:hover {
    background-color: inherit;
}

/* 修改排序圖標樣式，只改變箭頭顏色，保持文字顏色不變 */
.sort-icon {
    font-size: 0.85rem;
    margin-left: 5px;
    display: inline-block;
    vertical-align: middle;
    color: #6c757d; /* 預設時的灰色 */
    opacity: 1; /* 確保預設時就有顏色 */
    font-weight: normal;
}

/* 激活的排序圖標 - 深色模式使用白色 */
th[data-sorted="true"] .sort-icon {
    color: #FFFFFF !important; /* 純白色 */
    font-weight: bold;
}

/* 激活的排序欄位標題 - 保持文字顏色不變 */
th[data-sorted="true"] {
    font-weight: bold;
    /* 不設置文字顏色，保持原有顏色 */
}

/* 淺色模式下的排序圖標 - 使用藍色 */
.light-table th[data-sorted="true"] .sort-icon {
    color: #0d6efd !important; /* 藍色 */
}

/* 淺色模式下的排序欄位標題 - 保持文字顏色不變 */
.light-table th[data-sorted="true"] {
    font-weight: bold;
    /* 不設置文字顏色，保持原有顏色 */
}

/* 功能按鈕樣式 - 無外框版本 */
.btn-action {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background-color: transparent;
    border: none;
    transition: all 0.2s ease;
    color: #ffffff;
}

.btn-action:hover {
    background-color: rgba(33, 11, 11, 0.1);
    color: lightblue;
}

.btn-action:focus {
    box-shadow: none;
    outline: none;
}

.btn-action i {
    font-size: 24px;
}

/* 淺色模式下的按鈕樣式 */
.light-table .btn-action {
    color: #6c757d;
}

.light-table .btn-action:hover {
    background-color: rgba(108, 117, 125, 0.1);
    color: #212529;
}