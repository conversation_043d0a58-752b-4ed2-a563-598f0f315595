.layoutBackGround {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-attachment:fixed;
	background-repeat:no-repeat;
	background-image: url(/src/assets/images/layoutBackground.png);
	background-size: cover;
	overflow-y: auto;
}

.layoutBackGround::-webkit-scrollbar {
    display: none;
}


/* 修正 Modal 相關樣式 */
.modal {
    z-index: 9999 !important;
    pointer-events: auto !important;
}

.modal-backdrop {
    z-index: 9998 !important;
}

/* 確保其他固定元素不會覆蓋 Modal */
.filter-container, 
.layoutBackGround, 
.fullscreen-overlay, 
.loader {
    z-index: 1000 !important;
}

/* 確保 Modal 內容可點擊 */
.modal-dialog {
    pointer-events: auto !important;
}

.modal-content {
    pointer-events: auto !important;
}

.modal-footer {
    pointer-events: auto !important;
}

.modal-footer .btn {
    pointer-events: auto !important;
    position: relative !important;
    z-index: 10000 !important;
}

/* 確保其他固定元素不會覆蓋 Modal 內容 */
.loader {
    z-index: 1000 !important;
}

