/* 登入容器樣式 */
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  
  /* 背景圖片設置 */
  background-image: url(/src/assets/images/background.png);
  background-position: center bottom 30%;;
  background-repeat: no-repeat;
  background-size: cover;
  background-color: black; /* 背景色，防止圖片加載前的白色閃爍 */
}



.rect {
  width: 500px;
  height: 500px;
  background-color: rgba(255, 255, 255, 0.086);
  backdrop-filter: blur(10px);
  border-radius: 40.47px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loginTitle {
  position: absolute;
  top: 20%;
  left: 50%;
  transform: translate(-50%, -50%);

}

.content {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.titleTxt {
  position: absolute;
  top: -8%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 25px;
  line-height: 24px;
  white-space: nowrap;
  color: white;
  margin-bottom: 20px;
}

.form-control {
  width: 400px;
  height: 40px;
  color: white;
  background-color: transparent;
  backdrop-filter: blur(5000px);
  border: 1px solid slategray;
}

.inputText{
  text-align: left;
  display: block;
  width: 100%;
  font-size: 18px;
  color:white;
  margin-bottom: 5px;
}

.login {
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: transparent;
  margin-top: 20px;
}

.loginBtn {
  margin-top: 20px;
  padding: 8px 30px;
  font-size: 18px;
}

.Login .btn:hover {
  background-color: white;
  color: black;
}

.form-group {
  margin: 1rem;
}

.mx-1 {
  margin-left: 0.3rem;
  margin-right: 0.3rem;
}

.mb-1 {
  margin-bottom: 0.3rem;
}

.copyright {
  position: absolute;
  bottom: 40px; left: 40px;
  min-width: 0;
  font: 400 16px/1.5 Roboto, Helvetica, Arial, serif;
  color: white;
}