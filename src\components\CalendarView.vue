<template>
    <div class="card mt-2 bg-transparent border-light rounded-top-4" :class="{'light-card': !isDarkMode, 'dark-card': isDarkMode}">
        <div class="card-header" :class="{'light-header': !isDarkMode, 'dark-header': isDarkMode}">
            <div class="row align-items-center">
                <div class="col-6 d-flex align-items-center">
                  <DatePicker 
                    v-model="selectedDate" 
                    :input-class="{'light-input': !isDarkMode, 'dark-input': isDarkMode}"
                  />
                </div>
                <div class="col-6 d-flex justify-content-end align-items-center">
                    <div class="theme-toggle d-flex align-items-center">
                        <span class="me-2 small" :class="{'text-light': isDarkMode, 'text-dark': !isDarkMode}">{{ isDarkMode ? '深色版' : '淺色版' }}</span>
                        <div class="form-check form-switch mb-0">
                            <input class="form-check-input" type="checkbox" id="themeSwitch" v-model="isDarkMode" @change="toggleTheme">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 導航控制區域 -->
        <div class="card-header bg-gradient border-light" :class="{'light-header': !isDarkMode, 'dark-header': isDarkMode}">
            <div class="row">
                <!-- 日期導航控制 -->
                <div class="col-6 d-flex align-items-center">
                    <i class="fa fa-caret-left fa-2x me-2" 
                       :class="{'text-dark': !isDarkMode, 'text-light': isDarkMode}" 
                       @click="navigateDate(-1)"
                       title="前一天"></i>
                    <span class="fs-5 text-nowrap" 
                          :class="{'text-dark': !isDarkMode, 'text-light': isDarkMode}">{{ formattedDate }}</span>
                    <i class="fa fa-caret-right fa-2x ms-2" 
                       :class="{'text-dark': !isDarkMode, 'text-light': isDarkMode}" 
                       @click="navigateDate(1)"
                       title="後一天"></i>
                </div>
                <!-- 視圖選擇控制 -->
                <div class="col-6 d-flex align-items-center justify-content-end">
                    <select class="form-select view-width" v-model="selectedView" :class="{'light-select': !isDarkMode, 'dark-select': isDarkMode}">
                        <option>日</option>
                        <option>週</option>
                        <option>月</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- 日曆內容區域 -->
        <div class="card-body flex-grow-1 m-0 p-0" :class="{'light-table': !isDarkMode, 'dark-table': isDarkMode}">
            <!-- 日視圖 -->
            <template v-if="selectedView === '日'">
                <div class="day-view-container">
                    <div class="calendar-table-container position-relative" :class="{'light-table': !isDarkMode, 'dark-table': isDarkMode}" ref="tableContainer">
                        <!-- 添加固定在表頭的左右捲動按鈕 -->
                        <div class="scroll-buttons-container">
                            <button v-show="hasHorizontalScroll" class="scroll-control-btn scroll-left-btn" :class="{'light-btn': !isDarkMode, 'dark-btn': isDarkMode}" @click="scrollTableHorizontally(-200)">
                                <i class="fa fa-chevron-left"></i>
                            </button>
                            <button v-show="hasHorizontalScroll" class="scroll-control-btn scroll-right-btn" :class="{'light-btn': !isDarkMode, 'dark-btn': isDarkMode}" @click="scrollTableHorizontally(200)">
                                <i class="fa fa-chevron-right"></i>
                            </button>
                        </div>
                        
                        <table class="calendar-table table table-bordered table-striped" :class="{'table-light': !isDarkMode, 'table-dark': isDarkMode}">
                            <thead>
                                <tr>
                                    <th class="time-column" :class="{'light-header-cell': !isDarkMode, 'dark-header-cell': isDarkMode}">時間</th>
                                    <th v-for="room in props.selectedRooms" :key="room.id" :class="{'light-header-cell': !isDarkMode, 'dark-header-cell': isDarkMode}">
                                        <div class="room-name" 
                                             @mouseenter="showTooltip($event, getRoomTooltip(room))"
                                             @mouseleave="hideTooltip">
                                            {{room.name}}
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="hour in 24" :key="hour-1" :data-hour="hour-1">
                                    <td class="time-column">{{(hour-1).toString().padStart(2, '0')}}:00</td>
                                    <template v-for="room in props.selectedRooms" :key="room.id">
                                        <!-- 無論是否有會議或跨時間段會議，都渲染可點擊的單元格 -->
                                        <td class="position-relative available-cell"
                                            @click="openModal(room, hour-1)">
                                            <!-- 透明可點擊區域，確保整個單元格都可點擊 -->
                                            <div class="cell-clickable-area" @click.stop="openModal(room, hour-1)"></div>
                                            
                                            <!-- 顯示該時間段的會議 -->
                                            <div v-for="meeting in getMeetingsForRoomAndHour(room.id, hour-1)" 
                                                 :key="meeting.id"
                                                 class="meeting-cell"
                                                 :class="{'non-self-meeting': meeting.isSelf === false}"
                                                 :style="{
                                                   top: `${getMeetingTopOffset(meeting)}%`,
                                                   height: `${getMeetingHeight(meeting)}%`,
                                                   position: 'absolute',
                                                   width: 'calc(100% - 4px)'
                                                 }"
                                                 @click.stop="showMeetingDetails(meeting)"
                                                 @mouseenter="showTooltip($event, getMeetingTooltip(meeting))"
                                                 @mouseleave="hideTooltip">
                                                <div class="meeting-title">{{ meeting.subject || '未命名會議' }}</div>
                                                <div class="meeting-time">{{ meeting.contactName || '未知聯絡人' }}</div>
                                                <div class="meeting-host">{{ meeting.contactPhone || '未知聯絡電話' }}</div>
                                            </div>
                                        </td>
                                    </template>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </template>
            
            <!-- 週視圖 -->
            <div v-if="selectedView === '週'">
                <div class="calendar-table-container" :class="{'light-table': !isDarkMode, 'dark-table': isDarkMode}">
                    <table class="calendar-table table table-bordered table-striped" :class="{'table-light': !isDarkMode, 'table-dark': isDarkMode}">
                        <thead>
                            <tr>
                                <th class="time-column" :class="{'light-header-cell': !isDarkMode, 'dark-header-cell': isDarkMode}">時間</th>
                                <th v-for="(dayDate, index) in weekDates" :key="index" class="day-column" :class="{'light-header-cell': !isDarkMode, 'dark-header-cell': isDarkMode}">
                                    <div>{{ ['一', '二', '三', '四', '五', '六', '日'][index] }}</div>
                                    <div>{{ dayDate.getDate() }}</div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="hour in 24" :key="hour-1" :data-hour="hour-1">
                                <td class="time-column">{{(hour-1).toString().padStart(2, '0')}}:00</td>
                                <td v-for="(dayDate, dayIndex) in weekDates" :key="dayIndex" 
                                    class="position-relative available-cell"
                                    @click="openModal(null, hour-1, dayDate)">
                                    <!-- 顯示該日期和時間的前3個會議 -->
                                    <div v-for="(meeting, index) in getMeetingsForDateAndHour(dayDate, hour-1).slice(0, 3)" 
                                         :key="meeting.id"
                                         class="meeting-cell-container week-meeting"
                                         :class="{'short-meeting': getMeetingDuration(meeting) < 1}"
                                         :style="{
                                             top: getMeetingTopOffset(meeting) + '%',
                                             height: Math.max(getMeetingHeight(meeting), 30) + '%',
                                             width: '25%',
                                             left: `${index * 25}%`
                                         }"
                                         @click.stop="showMeetingDetails(meeting)"
                                         @mouseenter="showTooltip($event, getMeetingTooltip(meeting))"
                                         @mouseleave="hideTooltip">
                                        <div class="meeting-cell" :class="{'non-self-meeting': meeting.isSelf === false}">
                                            <div class="character-per-line">
                                                <span v-for="(char, charIndex) in getRoomNameById(meeting.roomId)" :key="charIndex" class="room-name-char">
                                                    {{ char }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 如果有超過3個會議，顯示"更多"提示，放在最右邊 -->
                                    <div v-if="getMeetingsForDateAndHour(dayDate, hour-1).length > 3" 
                                         class="meeting-cell-container week-meeting week-meeting-more-container"
                                         :style="{
                                             top: '0%',
                                             height: '100%',
                                             width: '25%',
                                             left: '75%'
                                         }"
                                         @click.stop="showMoreMeetingsForDateAndHour(dayDate, hour-1)"
                                         @mouseenter="showTooltip($event, `${formatDayDateToRoc(dayDate)} ${hour-1}:00<br>還有 ${getMeetingsForDateAndHour(dayDate, hour-1).length - 3} 場其他會議`)"
                                         @mouseleave="hideTooltip">
                                        <div class="meeting-cell more-meetings-cell">
                                            <div class="character-per-line more-character-line">
                                                <span class="room-name-char">還</span>
                                                <span class="room-name-char">有</span>
                                                <span class="room-name-char">{{ getMeetingsForDateAndHour(dayDate, hour-1).length - 3 }}</span>
                                                <span class="room-name-char">個</span>
                                                <span class="room-name-char">會</span>
                                                <span class="room-name-char">議</span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 月視圖 -->
            <div v-if="selectedView === '月'" class="calendar-table-container" :class="{'light-table': !isDarkMode, 'dark-table': isDarkMode}">
                <table id="monthly_table" class="table table-bordered table-striped" :class="{'table-light': !isDarkMode, 'table-dark': isDarkMode}">
                    <thead>
                        <tr>
                            <th class="text-center small" :class="{'text-dark': !isDarkMode, 'text-light': isDarkMode}">一</th>
                            <th class="text-center small" :class="{'text-dark': !isDarkMode, 'text-light': isDarkMode}">二</th>
                            <th class="text-center small" :class="{'text-dark': !isDarkMode, 'text-light': isDarkMode}">三</th>
                            <th class="text-center small" :class="{'text-dark': !isDarkMode, 'text-light': isDarkMode}">四</th>
                            <th class="text-center small" :class="{'text-dark': !isDarkMode, 'text-light': isDarkMode}">五</th>
                            <th class="text-center small" :class="{'text-dark': !isDarkMode, 'text-light': isDarkMode}">六</th>
                            <th class="text-center small" :class="{'text-dark': !isDarkMode, 'text-light': isDarkMode}">日</th>
                        </tr>
                    </thead>
                    <tbody class="border-top border-bottom">
                        <tr v-for="week in monthCalendar" :key="week.join('')">
                            <td v-for="(day, index) in week" :key="index" 
                                class="text-light text-nowrap text-center small"
                                :class="{
                                    'available-cell': day !== 0, 
                                    'text-muted': day === 0,
                                    'current-day': isCurrentDay(day, index)
                                }"
                                @click="day !== 0 ? openModal(null, null, getDateFromMonthDay(day)) : null">
                                <div class="h-100 d-flex flex-column">
                                    <div class="day-number">{{ day !== 0 ? day : '' }}</div>
                                    <div class="flex-grow-1 month-meetings-container">
                                        <!-- 只顯示前3個會議 -->
                                        <div v-for="(meeting, index) in getMeetingsForMonthDay(day).slice(0, 3)" 
                                             :key="meeting.id"
                                             class="month-meeting-item"
                                             :class="{'non-self-meeting': meeting.isSelf === false}"
                                             @click.stop="showMeetingDetails(meeting)"
                                             @mouseenter="showTooltip($event, getMeetingTooltip(meeting))"
                                             @mouseleave="hideTooltip">
                                            {{ getRoomNameById(meeting.roomId) }} {{ meeting.isPrivate ? '不公開' : meeting.subject }}
                                        </div>
                                        <!-- 如果有超過3個會議，顯示"更多"提示 -->
                                        <div v-if="getMeetingsForMonthDay(day).length > 3" 
                                             class="month-meeting-more"
                                             @click.stop="showMoreMeetings(day)"
                                             @mouseenter="showTooltip($event, `${formatDayDateToRoc(getDateFromMonthDay(day))} 還有 ${getMeetingsForMonthDay(day).length - 3} 場其他會議`)"
                                             @mouseleave="hideTooltip">
                                            還有 {{ getMeetingsForMonthDay(day).length - 3 }} 場其他會議...
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- 會議預約彈窗 -->
    <MeetingModal ref="meetingModalRef"
            :visible="showModal"
            :floors="floors"
            :defaultStartDate="meetingStartDate"
            :defaultEndDate="meetingEndDate"
            :defaultStartHour="meetingStartHour"
            :defaultStartMinute="meetingStartMinute"
            :defaultEndHour="meetingEndHour"
            :defaultEndMinute="meetingEndMinute"
            :defaultRoomId="meetingRoomId"
            :meetingId="currentMeetingId"
            :meetingDetails="currentMeetingDetails"
            :isViewMode="isViewMode"
            @update:visible="showModal = $event"
            @confirm="onConfirm"
            @refresh-data="fetchMeetings"></MeetingModal>
            
    <!-- 更多會議列表視窗 -->
    <MeetingsModal
      :visible="showMeetingsModal"
      :title="meetingsModalTitle"
      :meetings="meetingsModalList"
      :getRoomNameById="getRoomNameById"
      @update:visible="showMeetingsModal = $event"
      @view-meeting="showMeetingDetails"
    ></MeetingsModal>
    
    <!-- 自定義 tooltip -->
    <div class="custom-tooltip" ref="tooltip" v-show="tooltipVisible" :style="tooltipStyle">
        <div class="tooltip-inner" v-html="tooltipContent"></div>
    </div>
</template>
<style>
    /* 引入 Font Awesome 圖標庫 */
    @import url(https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css);
    
    /* 日期選擇器寬度設定 */
    .date-width {
        width: 140px;
        margin-right: 10px;
    }
    
    /* 視圖選擇器寬度設定 */
    .view-width {
        width: 80px;
    }
    
    /* 主題切換開關樣式 */
    .theme-toggle {
        display: flex;
        align-items: center;
    }

    .form-check-input {
        cursor: pointer;
        width: 40px;
        height: 20px;
    }
    
    .form-check-label {
        cursor: pointer;
        font-size: 0.875rem;
    }
    
    /* 深色卡片樣式 - 默認 */
    .dark-card {
        background-color: transparent !important;
        border-color: rgba(255, 255, 255, 0.175) !important;
    }
    
    /* 淺色卡片樣式 */
    .light-card {
        background-color: #ffffff !important;
        border-color: #dee2e6 !important;
    }
    
    /* 深色頭部樣式 */
    .dark-header {
        background-color: rgba(33, 37, 41, 0.8) !important;
        border-color: rgba(255, 255, 255, 0.175) !important;
    }
    
    /* 淺色頭部樣式 */
    .light-header {
        background-color: #f8f9fa !important;
        border-color: #dee2e6 !important;
    }
    
    /* 深色表頭單元格樣式 */
    .dark-header-cell {
        background-color: #212529 !important;
        color: #fff !important;
    }
    
    /* 淺色表頭單元格樣式 */
    .light-header-cell {
        background-color: #f8f9fa !important;
        color: #212529 !important;
    }
    
    /* 深色輸入框樣式 */
    .dark-input {
        background-color: #343a40 !important;
        border-color: rgba(255, 255, 255, 0.175) !important;
        color: #fff !important;
    }
    
    /* 淺色輸入框樣式 */
    .light-input {
        background-color: #fff !important;
        border-color: #ced4da !important;
        color: #212529 !important;
    }
    
    /* 深色選擇框樣式 */
    .dark-select {
        background-color: #343a40 !important;
        border-color: rgba(255, 255, 255, 0.175) !important;
        color: #fff !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e") !important;
        background-repeat: no-repeat !important;
        background-position: right 0.75rem center !important;
        background-size: 16px 12px !important;
        appearance: none !important;
    }
    
    /* 淺色選擇框樣式 */
    .light-select {
        background-color: #fff !important;
        border-color: #ced4da !important;
        color: #212529 !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23212529' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e") !important;
        background-repeat: no-repeat !important;
        background-position: right 0.75rem center !important;
        background-size: 16px 12px !important;
        appearance: none !important;
    }
    
    /* 淺色表格樣式 */
    .light-table .table-light {
        --bs-table-bg: #ffffff;
        --bs-table-striped-bg: #f8f9fa;
        --bs-table-striped-color: #212529;
        --bs-table-active-bg: #dee2e6;
        --bs-table-active-color: #212529;
        --bs-table-hover-bg: #e9ecef;
        --bs-table-hover-color: #212529;
        color: #212529;
        border-color: #dee2e6;
    }

    .light-table .table-light th,
    .light-table .table-light td {
        border-color: #dee2e6;
        color: #212529;
    }

    /* 月檢視中的會議項目樣式 */
    .month-meeting-item {
        background-color: rgba(255, 213, 128, 0.3);
        border: 1px solid rgba(255, 213, 128, 0.5);
        border-radius: 4px;
        margin: 2px;
        padding: 2px 4px;
        font-size: 0.75rem;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        transition: all 0.2s ease;
        max-width: 100%; /* 確保不超過容器寬度 */
        color: white; /* 確保文字在深色模式下清晰可見 */
    }

    .month-meeting-item:hover {
        background-color: rgba(255, 213, 128, 0.5);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    /* 月檢視會議項目樣式 - 深色模式 */
    #monthly_table .month-meeting-item {
        background-color: rgba(0, 123, 255, 0.3) !important; /* 改為藍色背景 */
        border: 1px solid rgba(0, 123, 255, 0.5) !important; /* 改為藍色邊框 */
        border-radius: 4px;
        margin: 2px;
        padding: 2px 4px;
        font-size: 0.75rem;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        transition: all 0.2s ease;
        max-width: 100%;
        color: white !important;
    }

    #monthly_table .month-meeting-item:hover {
        background-color: rgba(255, 213, 128, 0.5) !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    /* 月檢視中非自己的會議的灰色背景樣式 - 深色模式 */
    #monthly_table .month-meeting-item.non-self-meeting {
        background-color: #6c757d !important;
        border: 1px solid rgba(255, 213, 128, 0.5) !important;
        color: white !important;
    }

    #monthly_table .month-meeting-item.non-self-meeting:hover {
        background-color: #5a6268 !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    /* 淺色模式下的月檢視會議項目 */
    .light-table #monthly_table .month-meeting-item {
        background-color: rgba(0, 123, 255, 0.3) !important;
        border: 1px solid rgba(0, 123, 255, 0.5) !important;
        color: #333 !important;
    }

    .light-table #monthly_table .month-meeting-item:hover {
        background-color: rgba(0, 123, 255, 0.5) !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    /* 深色模式下月檢視中非自己的會議的灰色背景樣式 */
    .light-table #monthly_table .month-meeting-item.non-self-meeting {
        background-color: #6c757d !important;
        border: 1px solid rgba(255, 213, 128, 0.5) !important;
        color: white !important;
    }

    .light-table #monthly_table .month-meeting-item.non-self-meeting:hover {
        background-color: #5a6268 !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    /* 確保月檢視表格列寬固定 */
    #monthly_table {
        table-layout: fixed; /* 確保表格使用固定佈局 */
        width: 100%;
    }

    #monthly_table th, 
    #monthly_table td {
        width: calc(100% / 7); /* 7天平均分配寬度 */
        max-width: calc(100% / 7); /* 確保最大寬度也是固定的 */
        overflow: hidden; /* 隱藏溢出內容 */
        min-height: 100px; /* 添加最小高度 */
        height: 100px; /* 固定高度 */
    }

    /* 月檢視"更多"提示的樣式 */
    .month-meeting-more {
        background-color: rgba(0, 123, 255, 0.5);
        border: 1px solid rgba(255, 255, 255, 0.7);
        border-radius: 4px;
        margin: 2px;
        padding: 2px 4px;
        font-size: 0.75rem;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: bold;
        color: #ffffff;
    }

    .month-meeting-more:hover {
        background-color: rgba(0, 123, 255, 0.6);
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    }

    /* 淺色模式下的月檢視"更多"提示 */
    .light-table .month-meeting-more {
        background-color: rgba(0, 123, 255, 0.6);
        border: 1px solid rgba(0, 123, 255, 0.8);
        color: #fff;
    }

    .light-table .month-meeting-more:hover {
        background-color: rgba(0, 123, 255, 0.8);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }
    
    /* 深色表格樣式 */
    .dark-table .table-dark {
        --bs-table-bg: #212529;
        --bs-table-striped-bg: #2c3034;
        --bs-table-striped-color: #fff;
        --bs-table-active-bg: #373b3e;
        --bs-table-active-color: #fff;
        --bs-table-hover-bg: #323539;
        --bs-table-hover-color: #fff;
        color: #fff;
        border-color: #373b3e;
    }
    
    /* 修正日期選擇器在淺色模式下的日曆圖標 */
    .light-input::-webkit-calendar-picker-indicator {
        filter: invert(0);
    }
    
    /* 修正日期選擇器在深色模式下的日曆圖標 */
    .dark-input::-webkit-calendar-picker-indicator {
        filter: invert(1);
    }
    
    /* 新的日曆表格容器樣式 */
    .calendar-table-container {
        width: 100%;
        overflow-y: auto;
        max-height: 600px;
    }
    
    /* 新的日曆表格樣式 */
    .calendar-table {
        width: 100%;
        table-layout: fixed;
        border-collapse: collapse;
    }
    
    /* 時間列樣式 */
    .time-column {
        width: 140px !important;
        min-width: 140px !important;
        max-width: 140px !important;
        position: sticky;
        left: 0;
        z-index: 1;
        text-align: center;
        font-size: 0.875rem;
    }

    /* 日視圖中的會議室欄位標頭固定寬度 */
    .day-view-container .calendar-table th:not(.time-column) {
        width: 200px !important;
        min-width: 200px !important;
        max-width: 200px !important;
    }

    /* 日視圖中的會議室欄位內容與標頭寬度一致 */
    .day-view-container .calendar-table td:not(.time-column) {
        width: 200px !important;
        min-width: 200px !important;
        max-width: 200px !important;
    }

    /* 日視圖表格不填滿整個容器 */
    .day-view-container .calendar-table {
        width: auto !important;
        table-layout: fixed;
    }

    /* 確保表格容器可以水平滾動 */
    .calendar-table-container {
        width: 100%;
        overflow-x: auto;
        overflow-y: auto;
        max-height: calc(100vh - 200px); /* 設置最大高度，確保可以垂直滾動 */
        position: relative; /* 為絕對定位的子元素提供參考 */
    }

    /* 表格單元格樣式 - 週視圖保持140px高度 */
    .calendar-table td {
        height: 140px;
        vertical-align: top;
        text-align: center;
        color: white;
        font-size: 0.875rem;
        white-space: nowrap;
        padding: 2px;
        position: relative;
    }

    /* 日視圖單元格高度設置為80px */
    .day-view-container .calendar-table td {
        height: 80px; /* 將日視圖單元格高度從70px增加到80px */
    }

    /* 表格頭部樣式 - 只調整表頭高度 */
    .calendar-table thead th {
        height: 60px; /* 只有表頭高度減少到60px */
        position: sticky;
        top: 0;
        z-index: 30; /* 提高 z-index 值，確保在會議內容之上 */
        background-color: #212529; /* 深色模式下的背景色 */
        opacity: 1 !important; /* 確保不透明 */
        vertical-align: middle;
        text-align: center;
        color: white;
        font-size: 0.875rem;
        white-space: nowrap;
    }

    /* 淺色模式下的表頭樣式 */
    .light-table .calendar-table thead th {
        background-color: #f8f9fa; /* 淺色模式下的背景色 */
    }

    /* 時間列和表頭交叉處的樣式 */
    .calendar-table thead th.time-column {
        z-index: 3;
    }

    /* 可預約單元格的基本樣式 */
    .available-cell {
        cursor: pointer;
        position: relative;
        transition: all 0.2s ease;
    }

    /* 滑鼠經過可預約單元格時的效果 */
    .available-cell:hover {
        background-color: rgba(135, 132, 135, 0.3) !important; /* 淡紫色背景 */
        color: white !important;
        box-shadow: inset 0 0 15px rgba(135, 132, 135, 0.5);
    }

    /* 添加點擊效果 */
    .available-cell:active {
        background-color: rgba(135, 132, 135, 0.5) !important;
        transform: scale(0.98);
    }

    /* 會議單元格容器樣式 */
    .meeting-cell-container {
        position: absolute;
        left: 0;
        width: 100%;
        overflow: hidden;
        padding: 0;
        margin: 0;
        z-index: 10; /* 確保低於表頭的 z-index */
        min-height: 30px; /* 設置最小高度，確保短時間會議也能顯示內容 */
    }

    /* 會議單元格樣式 */
    .meeting-cell {
        background-color: rgba(255, 213, 128, 0.3);
        border: 1px solid rgba(255, 213, 128, 0.5);
        border-radius: 4px;
        padding: 8px;
        margin: 2px;
        text-align: left;
        font-size: 0.85rem;
        height: calc(100% - 4px);
        width: calc(100% - 4px);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition: all 0.2s ease;
    }

    /* 深色模式下的會議單元格樣式 - 只修改日視圖 */
    .light-table .day-view-container .meeting-cell {
        background-color: rgba(0, 123, 255, 0.3);
        border: 1px solid rgba(0, 123, 255, 0.7);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        color: #333;
    }

    .meeting-cell:hover {
        background-color: rgba(255, 213, 128, 0.5);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        transform: translateY(-2px);
    }

    /* 淺色模式下會議單元格懸停效果 - 只修改日視圖 */
    .light-table .day-view-container .meeting-cell:hover {
        background-color: rgba(0, 123, 255, 0.5);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    }

    /* 非自己的會議的灰色背景樣式 - 深色模式 */
    .meeting-cell.non-self-meeting {
        background-color: #6c757d !important; /* 灰色背景 */
        border-color: #495057 !important; /* 深灰色邊框 */
    }

    /* 深色模式下非自己的會議的灰色背景樣式 - 只修改日視圖 */
    .light-table .day-view-container .meeting-cell.non-self-meeting {
        background-color: #6c757d !important; /* 保持原來的灰色背景 */
        border-color: #495057 !important; /* 保持原來的深灰色邊框 */
        color: white !important; /* 確保文字在灰色背景上清晰可見 */
    }

    /* 會議標題樣式 */
    .meeting-title {
        font-weight: bold;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 4px;
    }

    /* 會議時間樣式 */
    .meeting-time {
        font-size: 0.8rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 2px;
    }

    /* 會議主持人樣式 */
    .meeting-host {
        font-size: 0.8rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* 深色模式下的會議文字顏色 - 只修改日視圖 */
    .light-table .day-view-container .meeting-cell .meeting-title,
    .light-table .day-view-container .meeting-cell .meeting-time,
    .light-table .day-view-container .meeting-cell .meeting-host {
        color: #333;
    }

    /* 深色模式下非自己會議的文字顏色 - 只修改日視圖 */
    .light-table .day-view-container .meeting-cell.non-self-meeting .meeting-title,
    .light-table .day-view-container .meeting-cell.non-self-meeting .meeting-time,
    .light-table .day-view-container .meeting-cell.non-self-meeting .meeting-host {
        color: white;
    }

    /* 一個字一個字換行的樣式 */
    .character-per-line {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start; /* 改為頂部對齊 */
        height: 100%;
        text-align: center;
        padding-top: 2px; /* 添加頂部內邊距 */
        width: 100%;
    }

    .room-name-char {
        color: #FFF;
        font-size: 0.7rem;
        font-weight: bold;
        line-height: 1.1;
        margin: 1px 0;
    }

    /* 深色模式下的字元樣式 */
    .light-table .room-name-char {
        color: #333;
    }

    /* 日期列樣式 */
    .day-column {
        min-width: 120px;
        text-align: center;
    }

    /* 月檢視會議項目容器 */
    .month-meeting-container {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        overflow-y: auto;
        max-height: 140px;
        min-height: 140px; /* 添加最小高度，確保即使沒有會議也保持高度 */
    }

    .month-meeting-item {
        background-color: rgba(255, 213, 128, 0.3);
        border-radius: 4px;
        margin: 2px;
        padding: 2px 4px;
        font-size: 0.75rem;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
        border: 1px solid rgba(255, 213, 128, 0.5);
        transition: all 0.2s ease;
        max-width: 100%; /* 確保不超過容器寬度 */
    }

    /* 月檢視會議容器 */
    .month-meetings-container {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        overflow-y: auto;
        max-height: 100px;
        min-height: 100px; /* 添加最小高度，確保即使沒有會議也保持高度 */
        width: 100%; /* 確保容器佔滿整個單元格寬度 */
    }



    /* 確保月檢視表格列寬固定 */
    #monthly_table {
        table-layout: fixed; /* 確保表格使用固定佈局 */
        width: 100%;
    }

    #monthly_table th, 
    #monthly_table td {
        width: calc(100% / 7); /* 7天平均分配寬度 */
        max-width: calc(100% / 7); /* 確保最大寬度也是固定的 */
        overflow: hidden; /* 隱藏溢出內容 */
    }

    /* 非自己的會議的灰色背景樣式 */
    .meeting-cell-container .meeting-cell.non-self-meeting {
        background-color: #6c757d !important; /* 灰色背景 */
        border-color: #495057 !important; /* 深灰色邊框 */
    }

    /* 深色模式下非自己的會議的灰色背景樣式 */
    .light-table .meeting-cell-container .meeting-cell.non-self-meeting {
        background-color: #adb5bd !important; /* 淺灰色背景 */
        border-color: #6c757d !important; /* 灰色邊框 */
    }

    /* 確保所有單元格都可點擊 */
    .calendar-table td {
        position: relative;
        cursor: pointer;
        pointer-events: auto !important;
    }

    /* 透明可點擊區域 */
    .cell-clickable-area {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        cursor: pointer;
        pointer-events: auto !important;
    }

    /* 確保會議單元格容器不會阻止底層單元格的點擊事件 */
    .meeting-cell-container {
        position: absolute;
        z-index: 10;
        pointer-events: auto !important;
    }

    /* 確保會議單元格本身可以點擊 */
    .meeting-cell {
        pointer-events: auto !important;
        z-index: 15;
    }

    /* 修正跨時間段會議的樣式，確保不會阻止點擊 */
    .spanning-meeting-indicator {
        position: absolute;
        z-index: 5;
        pointer-events: none !important; /* 確保不會阻止點擊 */
    }

    /* 確保空單元格也可點擊 */
    .available-cell {
        position: relative;
        min-height: 30px;
        cursor: pointer;
        pointer-events: auto !important;
    }

    /* 修正可能存在的覆蓋層問題 */
    .day-view-container .calendar-table td:not(.time-column)::before,
    .day-view-container .calendar-table td:not(.time-column)::after {
        pointer-events: none !important;
    }

    /* 自定義 tooltip 樣式 */
    .custom-tooltip {
        position: fixed;
        z-index: 9999;
        transform: translateX(-50%) translateY(-100%);
        pointer-events: none;
    }

    .tooltip-inner {
        background-color: rgba(255, 255, 255, 0.9);
        color: black;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        max-width: 250px;
        min-width: 200px;
        text-align: left;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .tooltip-inner::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: rgba(255, 255, 255, 0.9) transparent transparent transparent;
    }

    .room-name {
        cursor: pointer;
        position: relative;
        display: inline-block;
        width: 100%;
    }

    /* 會議單元格樣式 */
    .meeting-cell {
        cursor: pointer;
        position: relative;
        z-index: 2;
        padding: 4px;
        border-radius: 4px;
        background-color: rgba(0, 123, 255, 0.7);
        color: white;
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .meeting-cell:hover {
        background-color: rgba(0, 123, 255, 0.9);
    }

    .non-self-meeting {
        background-color: rgba(108, 117, 125, 0.7);
    }

    .non-self-meeting:hover {
        background-color: rgba(108, 117, 125, 0.9);
    }

    /* 非自己的會議的灰色背景樣式 - 深色模式下的日視圖 */
    .day-view-container .meeting-cell.non-self-meeting {
        background-color: #6c757d !important; /* 灰色背景 */
        border: 1px solid rgba(255, 213, 128, 0.5) !important; /* 金黃色框線 */
        color: white !important; /* 確保文字在灰色背景上清晰可見 */
    }

    /* 深色模式下非自己會議的文字顏色 - 只修改日視圖 */
    .day-view-container .meeting-cell.non-self-meeting .meeting-title,
    .day-view-container .meeting-cell.non-self-meeting .meeting-time,
    .day-view-container .meeting-cell.non-self-meeting .meeting-host {
        color: white;
    }

    /* 凍結時間列，使其在水平滾動時保持可見 */
    .calendar-table .time-column {
        position: sticky;
        left: 0;
        z-index: 20;
        background-color: #212529 !important; /* 深色模式下的背景色 */
        color: #fff !important; /* 深色模式下的文字顏色 */
        min-width: 80px;
        width: 80px;
        max-width: 80px;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2); /* 添加陰影效果，增強視覺分隔 */
    }

    /* 淺色模式下的時間列背景色 */
    .light-table .calendar-table .time-column {
        background-color: #f8f9fa !important; /* 淺色模式下的背景色 */
        color: #212529 !important; /* 淺色模式下的文字顏色 */
    }

    /* 凍結表頭，使其在垂直滾動時保持可見 */
    .calendar-table thead th {
        position: sticky;
        top: 0;
        z-index: 30; /* 確保表頭在時間列之上 */
        background-color: #212529 !important; /* 深色模式下的背景色 */
        color: #fff !important; /* 深色模式下的文字顏色 */
        opacity: 1 !important; /* 確保不透明 */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 添加陰影效果，增強視覺分隔 */
    }

    /* 淺色模式下的表頭背景色 */
    .light-table .calendar-table thead th {
        background-color: #f8f9fa !important; /* 淺色模式下的背景色 */
        color: #212529 !important; /* 淺色模式下的文字顏色 */
    }

    /* 表頭中的時間列需要更高的 z-index，確保它在水平和垂直滾動時都可見 */
    .calendar-table thead .time-column {
        z-index: 50; /* 確保它在所有元素之上 */
        position: sticky;
        left: 0;
        top: 0;
        background-color: #212529 !important; /* 深色模式下的背景色 */
        color: #fff !important; /* 深色模式下的文字顏色 */
        opacity: 1 !important; /* 確保不透明 */
    }

    /* 淺色模式下的表頭時間列背景色 */
    .light-table .calendar-table thead .time-column {
        background-color: #f8f9fa !important; /* 淺色模式下的背景色 */
        color: #212529 !important; /* 淺色模式下的文字顏色 */
    }

    /* 確保深色模式下時間列的文字顏色正確 */
    .calendar-table td.time-column {
        color: #fff !important;
        background-color: #212529 !important;
    }

    /* 確保深色模式下的時間文字正確顯示 */
    .time-text {
        color: #fff !important;
    }

    /* 淺色模式下的時間文字 */
    .light-table .time-text {
        color: #212529 !important;
    }

    /* 確保表格容器可以水平滾動 */
    .calendar-table-container {
        width: 100%;
        overflow-x: auto;
        overflow-y: auto;
        max-height: calc(100vh - 200px); /* 設置最大高度，確保可以垂直滾動 */
    }

    /* 確保表格邊框正確顯示 */
    .calendar-table {
        border-collapse: separate;
        border-spacing: 0;
    }

    /* 添加陰影效果，增強視覺分隔 */
    .calendar-table .time-column {
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
    }

    .calendar-table thead th {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    /* 表頭中的時間列需要兩個方向的陰影 */
    .calendar-table thead .time-column {
        box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
    }

    /* 修正淺色模式下時間軸的樣式 */
    .light-table .calendar-table .time-column {
        background-color: #f8f9fa; /* 淺色模式下的背景色 */
        color: #212529; /* 淺色模式下的文字顏色 */
        border-color: #dee2e6; /* 淺色模式下的邊框顏色 */
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1); /* 調整陰影效果 */
    }

    /* 確保淺色模式下時間列的文字顏色正確 */
    .light-table .calendar-table td.time-column {
        color: #212529 !important;
        background-color: #f8f9fa !important;
    }

    /* 確保淺色模式下表頭時間列的樣式正確 */
    .light-table .calendar-table thead .time-column {
        background-color: #f8f9fa !important;
        color: #212529 !important;
        border-color: #dee2e6 !important;
        box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
    }

    /* 捲動按鈕容器 */
    .scroll-buttons-container {
        position: sticky;
        top: 0;
        left: 0;
        width: 100%;
        height: 0;
        z-index: 60;
        pointer-events: none; /* 容器本身不接收點擊事件 */
    }

    /* 橫向捲軸控制按鈕基本樣式 - 增加透明度 */
    .scroll-control-btn {
        position: absolute;
        top: 10px;
        width: 36px;
        height: 36px;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.7; /* 增加透明度，從0.85降低到0.7 */
        transition: all 0.2s ease;
        pointer-events: auto;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(3px); /* 增加模糊效果，從2px增加到3px */
    }

    /* 向左捲動按鈕 - 精確調整位置到時間列和第一個會議室之間的線中間 */
    .scroll-left-btn {
        left: 80px; /* 時間列寬度 */
        transform: translateX(-50%); /* 使按鈕中心點對齊線中間 */
    }

    /* 向右捲動按鈕 */
    .scroll-right-btn {
        right: 5px; /* 進一步增加與右側的距離 */
    }

    /* 深色模式按鈕樣式 - 增加透明度 */
    .dark-btn {
        background-color: rgba(234, 221, 255, 0.7) !important; /* 增加透明度，從0.85降低到0.7 */
        color: #000 !important;
        border: 2px solid rgba(208, 188, 255, 0.8); /* 增加透明度，從0.9降低到0.8 */
    }

    /* 淺色模式按鈕樣式 - 增加透明度 */
    .light-btn {
        background-color: rgba(13, 110, 253, 0.7) !important; /* 增加透明度，從0.85降低到0.7 */
        color: #fff !important;
        border: 2px solid rgba(10, 88, 202, 0.8); /* 增加透明度，從0.9降低到0.8 */
    }

    /* 滑鼠懸停效果 - 懸停時保持較高不透明度 */
    .scroll-left-btn:hover {
        opacity: 0.9; /* 懸停時的不透明度，從1降低到0.9 */
        background-color: rgba(208, 188, 255, 0.9) !important; /* 懸停時的背景透明度，從0.95降低到0.9 */
        color: #000 !important;
        border-color: rgba(234, 221, 255, 0.9); /* 懸停時的邊框透明度，從0.95降低到0.9 */
        transform: translateX(-50%) scale(1.1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    }

    /* 淺色模式下的左側按鈕懸停效果 */
    .light-btn.scroll-left-btn:hover {
        background-color: rgba(11, 94, 215, 0.9) !important; /* 懸停時的背景透明度，從0.95降低到0.9 */
        color: #fff !important;
        border-color: rgba(10, 88, 202, 0.9); /* 懸停時的邊框透明度，從0.95降低到0.9 */
    }

    /* 右側按鈕懸停效果 */
    .scroll-right-btn:hover {
        opacity: 0.9; /* 懸停時的不透明度，從1降低到0.9 */
        background-color: rgba(208, 188, 255, 0.9) !important; /* 懸停時的背景透明度，從0.95降低到0.9 */
        color: #000 !important;
        border-color: rgba(234, 221, 255, 0.9); /* 懸停時的邊框透明度，從0.95降低到0.9 */
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    }

    /* 淺色模式下的右側按鈕懸停效果 */
    .light-btn.scroll-right-btn:hover {
        background-color: rgba(11, 94, 215, 0.9) !important; /* 懸停時的背景透明度，從0.95降低到0.9 */
        color: #fff !important;
        border-color: rgba(10, 88, 202, 0.9); /* 懸停時的邊框透明度，從0.95降低到0.9 */
    }

    /* 按鈕圖標樣式 */
    .scroll-control-btn i {
        font-size: 16px; /* 調整圖標大小 */
    }

    /* 確保會議室標題有相對定位，以便按鈕可以相對於它定位 */
    .room-header {
        position: relative;
    }

    /* 日期導航箭頭樣式 */
    .fa-caret-left, .fa-caret-right {
        cursor: pointer; /* 改變游標為指針形狀 */
        transition: transform 0.2s ease, color 0.2s ease; /* 添加過渡效果 */
    }

    /* 滑鼠經過時的效果 */
    .fa-caret-left:hover, .fa-caret-right:hover {
        transform: scale(1.2); /* 稍微放大 */
        color: #D0BCFF !important; /* 使用紫色，與按鈕懸停顏色一致 */
    }

    /* 淺色模式下滑鼠經過時的效果 */
    .light-header .fa-caret-left:hover, .light-header .fa-caret-right:hover {
        color: #0d6efd !important; /* 淺色模式下使用藍色 */
    }

    /* 點擊時的效果 */
    .fa-caret-left:active, .fa-caret-right:active {
        transform: scale(0.95); /* 稍微縮小，產生按下效果 */
    }
</style>
<script setup>
import { ref, defineProps, watch, onMounted, onUnmounted, computed, nextTick, reactive } from 'vue'
import MeetingModal from './modal/MeetingModal.vue'
import MeetingsModal from './modal/MeetingsModal.vue'
import DatePicker from './common/DatePicker.vue'
import api from '../utils/api'
import { formatToRocDateTime } from '../utils/dateUtils'

// Tooltip 相關變量
const tooltip = ref(null);
const tooltipVisible = ref(false);
const tooltipContent = ref('');
const tooltipStyle = reactive({
  left: '0px',
  top: '0px'
});

// 顯示 tooltip
const showTooltip = (event, content) => {
  if (!content) return;
  
  tooltipContent.value = content;
  
  // 計算位置
  const rect = event.currentTarget.getBoundingClientRect();
  tooltipStyle.left = `${rect.left + rect.width / 2}px`;
  tooltipStyle.top = `${rect.top - 10}px`;
  
  // 顯示 tooltip
  tooltipVisible.value = true;
};

// 隱藏 tooltip
const hideTooltip = () => {
  tooltipVisible.value = false;
};

// 獲取會議室 tooltip 內容
const getRoomTooltip = (room) => {
  if (!room) return '';
  
  let tooltipText = '';
  
  // 添加會議室名稱
  tooltipText += `<strong>${room.name || '未知會議室'}</strong><br>`;
  
  // 添加容納人數
  if (room.capacity) {
    tooltipText += `容納人數: ${room.capacity}<br>`;
  }
  
  // 添加樓層
  if (room.floor) {
    tooltipText += `樓層: ${room.floor}<br>`;
  }
  
  // 添加設備信息
  if (room.hasOwnProperty('onlineMeeting')) {
    tooltipText += `${room.onlineMeeting ? '✓ 視訊設備' : '✗ 無視訊設備'}<br>`;
  }
  
  if (room.hasOwnProperty('envDevice')) {
    tooltipText += `${room.envDevice ? '✓ 環控設備' : '✗ 無環控設備'}`;
  }
  
  return tooltipText;
};

// 獲取會議的提示信息
const getMeetingTooltip = (meeting) => {
  if (!meeting) return '';
  
  // 使用 dateUtils 中的 formatToRocDateTime 函數
  const startRoc = formatToRocDateTime(meeting.startTime);
  
  let tooltip = `<div class="tooltip-title">${meeting.subject || '未命名會議'}</div>`;
  tooltip += `<div>${startRoc.split(' ').slice(0, -1).join(' ')}</div>`; // 只取日期部分
  tooltip += `<div>${startRoc.split(' ').slice(-1)[0]} - ${formatToRocDateTime(meeting.endTime).split(' ').slice(-1)[0]}</div>`; // 取時間部分
  tooltip += `<div>會議室：${getRoomNameById(meeting.roomId)}</div>`;
  tooltip += `<div>聯絡人：${meeting.contactName || '未知'}</div>`;
  tooltip += `<div>聯絡電話：${meeting.contactPhone || '未知'}</div>`;
  
  return tooltip;
};

// 格式化會議時間
const formatMeetingTime = (meeting) => {
  if (!meeting || !meeting.startTime || !meeting.endTime) return '';
  
  const startTime = meeting.startTime.split(' ')[1].substring(0, 5);
  const endTime = meeting.endTime.split(' ')[1].substring(0, 5);
  
  return `${startTime} - ${endTime}`;
};

// 格式化日期為民國年格式
const formatToRocDate = (date) => {
  if (!date) return '';
  
  const year = date.getFullYear();
  const rocYear = year - 1911; // 轉換為民國年
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  
  return `${rocYear} 年 ${month} 月 ${day} 日`;
};

// 初始化日期為台灣時間的今天
const initTaiwanDate = () => {
  // 獲取當前的 UTC 時間
  const now = new Date();
  
  // 轉換為台灣時間 (UTC+8)
  const taiwanTime = new Date(now.getTime() + (8 * 60 * 60 * 1000));
  
  // 格式化為 YYYY-MM-DD
  return taiwanTime.toISOString().substring(0, 10);
};

// 響應式狀態
const selectedDate = ref(initTaiwanDate()); // 當前選擇的日期，預設為台灣今天
const selectedView = ref('日') // 當前選擇的視圖，預設為日視圖
const showModal = ref(false) // 控制會議預約彈窗的顯示狀態
const selectedRoom = ref(null) // 當前選擇的會議室
const selectedHour = ref(null) // 當前選擇的時間
const selectedDay = ref(null) // 當前選擇的日期（週視圖或月視圖使用）
const meetings = ref([]) // 存儲會議資料

// 更多會議視窗相關狀態
const showMeetingsModal = ref(false);
const meetingsModalList = ref([]);
const meetingsModalTitle = ref('');

// 主題設置
const isDarkMode = ref(true); // 預設為深色模式

// 新增：存儲會議開始日期、時間和會議室的響應式變量
const meetingStartDate = ref('');
const meetingEndDate = ref(''); // 新增：結束日期
const meetingStartHour = ref('');
const meetingStartMinute = ref('');
const meetingEndHour = ref('');
const meetingEndMinute = ref('');
const meetingRoomId = ref('');

// 新增：用於查看會議詳情的變量
const isViewMode = ref(false);
const currentMeetingId = ref('');
const currentMeetingDetails = ref({});

// 新增：對模態框的引用
const meetingModalRef = ref(null);

// 接收父組件傳入的會議室列表
const props = defineProps({
  selectedRooms: Array,
  floors: Array
})

// 初始化主題
onMounted(() => {
  initTheme();
  // 其他初始化代碼...
  fetchMeetings();
  
  // 添加：頁面載入後滾動到 08:00 的位置
  nextTick(() => {
    scrollToBusinessHour();
  });
})

// 初始化主題
const initTheme = () => {
  const savedTheme = localStorage.getItem('tableTheme');
  if (savedTheme !== null) {
    isDarkMode.value = savedTheme === 'true';
  }
  applyTheme();
};

// 切換主題
const toggleTheme = () => {
  localStorage.setItem('tableTheme', isDarkMode.value);
  applyTheme();
};

// 應用主題
const applyTheme = () => {
  // 獲取日曆表格容器
  const tableContainer = document.querySelector('.calendar-table-container');
  if (tableContainer) {
    if (isDarkMode.value) {
      tableContainer.classList.add('dark-table');
      tableContainer.classList.remove('light-table');
      
      // 更新按鈕樣式
      document.querySelectorAll('.scroll-control-btn').forEach(btn => {
        btn.classList.add('dark-btn');
        btn.classList.remove('light-btn');
      });
    } else {
      tableContainer.classList.add('light-table');
      tableContainer.classList.remove('dark-table');
      
      // 更新按鈕樣式
      document.querySelectorAll('.scroll-control-btn').forEach(btn => {
        btn.classList.add('light-btn');
        btn.classList.remove('dark-btn');
      });
    }
    
    // 強制更新時間列樣式
    setTimeout(() => {
      const timeColumns = document.querySelectorAll('.time-column');
      timeColumns.forEach(col => {
        if (isDarkMode.value) {
          col.style.backgroundColor = '#212529';
          col.style.color = '#fff';
        } else {
          col.style.backgroundColor = '#f8f9fa';
          col.style.color = '#212529';
        }
      });
      
      // 強制更新時間文字樣式
      const timeTexts = document.querySelectorAll('.time-text');
      timeTexts.forEach(text => {
        if (isDarkMode.value) {
          text.style.color = '#fff';
        } else {
          text.style.color = '#212529';
        }
      });
    }, 0);
  }
  
  console.log('應用主題:', isDarkMode.value ? '深色' : '淺色');
};

// 計算屬性：格式化日期顯示
const formattedDate = computed(() => {
  const date = new Date(selectedDate.value);
  const year = date.getFullYear();
  const rocYear = year - 1911; // 轉換為民國年
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  
  if (selectedView.value === '日') {
    // 日視圖顯示單一日期
    return `${rocYear} 年 ${month} 月 ${day} 日`;
  } else if (selectedView.value === '週') {
    // 週視圖顯示一週的起始和結束日期
    const day = date.getDay(); // 0-6，0表示週日
    
    // 獲取本週的週一（而不是週日）
    const firstDay = new Date(date);
    // 如果今天是週日(0)，則設置為前一週的週一，否則設置為本週的週一
    firstDay.setDate(date.getDate() - (day === 0 ? 6 : day - 1));
    
    // 獲取本週的週日
    const lastDay = new Date(firstDay);
    lastDay.setDate(firstDay.getDate() + 6);
    
    const firstRocYear = firstDay.getFullYear() - 1911; // 轉換為民國年
    const firstMonth = (firstDay.getMonth() + 1).toString().padStart(2, '0');
    const firstDate = firstDay.getDate().toString().padStart(2, '0');
    
    const lastRocYear = lastDay.getFullYear() - 1911; // 轉換為民國年
    const lastMonth = (lastDay.getMonth() + 1).toString().padStart(2, '0');
    const lastDate = lastDay.getDate().toString().padStart(2, '0');
    
    // 如果起始和結束日期在同一年，只顯示一次年份
    if (firstRocYear === lastRocYear) {
      return `${firstRocYear} 年 ${firstMonth} 月 ${firstDate} 日 - ${lastMonth} 月 ${lastDate} 日`;
    } else {
      return `${firstRocYear} 年 ${firstMonth} 月 ${firstDate} 日 - ${lastRocYear} 年 ${lastMonth} 月 ${lastDate} 日`;
    }
  } else {
    // 月視圖顯示年月
    return `${rocYear} 年 ${month} 月`;
  }
})

// 計算屬性：生成月曆數據
const monthCalendar = computed(() => {
  const date = new Date(selectedDate.value);
  const year = date.getFullYear();
  const month = date.getMonth();
  
  // 獲取當月第一天
  const firstDay = new Date(year, month, 1);
  // 獲取當月第一天是星期幾（0-6，0表示週日）
  let startingDay = firstDay.getDay();
  // 調整為週一為第一天（0表示週一，6表示週日）
  startingDay = startingDay === 0 ? 6 : startingDay - 1;
  
  // 獲取當月天數
  const monthDays = new Date(year, month + 1, 0).getDate();
  
  // 生成月曆數據（6行7列的二維數組）
  const calendar = [];
  let day = 1;
  
  // 最多6週
  for (let i = 0; i < 6; i++) {
    const week = [];
    // 每週7天
    for (let j = 0; j < 7; j++) {
      if (i === 0 && j < startingDay) {
        // 當月第一天之前的空白
        week.push(0);
      } else if (day > monthDays) {
        // 當月最後一天之後的空白
        week.push(0);
      } else {
        // 當月的日期
        week.push(day++);
      }
    }
    calendar.push(week);
    // 如果已經填完當月所有日期，不再生成新的一週
    if (day > monthDays) break;
  }
  
  return calendar;
});

// 計算屬性：獲取週視圖的日期數組
const weekDates = computed(() => {
  const date = new Date(selectedDate.value);
  const day = date.getDay(); // 0-6，0表示週日
  const result = [];
  
  // 獲取本週的週一（而不是週日）
  const firstDay = new Date(date);
  // 如果今天是週日(0)，則設置為前一週的週一，否則設置為本週的週一
  firstDay.setDate(date.getDate() - (day === 0 ? 6 : day - 1));
  
  // 生成一週的日期（從週一開始）
  for (let i = 0; i < 7; i++) {
    const currentDate = new Date(firstDay);
    currentDate.setDate(firstDay.getDate() + i);
    result.push(currentDate);
  }
  
  return result;
});

// 新增：獲取會議資料的方法
const fetchMeetings = async () => {
  // 確保有選擇的會議室
  if (!props.selectedRooms || props.selectedRooms.length === 0) {
    console.log('沒有選擇會議室，不獲取會議資料');
    meetings.value = []; // 清空會議資料
    return;
  }
  
  try {
    // 獲取所有選擇的會議室ID
    const roomIds = props.selectedRooms.map(room => room.id);
    
    console.log('發送請求獲取會議資料:', {
      view: selectedView.value,
      date: selectedDate.value,
      roomIds: roomIds
    });
    
    // 呼叫API獲取會議資料
    const response = await api.post('/api/reserve/meetings', {
      view: selectedView.value,  // 日、週、月
      date: selectedDate.value,  // YYYY-MM-DD 格式
      roomIds: roomIds           // 會議室ID陣列
    }, {
      callback: function(response) {
        console.log('獲取會議資料成功:', response.data);
        
        // 確保回傳的資料是陣列
        if (response.data && response.data.meetings && Array.isArray(response.data.meetings)) {
          meetings.value = response.data.meetings;
        } else if (Array.isArray(response.data)) {
          meetings.value = response.data;
        } else if (response.data && typeof response.data === 'object') {
          // 如果回傳的是物件而不是陣列，嘗試從物件中提取會議資料
          meetings.value = [];
        } else {
          meetings.value = [];
        }
        
        // 檢查會議資料
        if (meetings.value.length > 0) {
          console.log('會議資料範例:', meetings.value[0]);
          console.log('會議室對應關係:');
          meetings.value.forEach(meeting => {
            console.log(`會議 "${meeting.subject}" 在會議室 ${meeting.roomName || '未知'} (ID: ${meeting.roomId})`);
          });
        } else {
          console.log('沒有會議資料');
        }
      }
    });
  } catch (error) {
    console.error('獲取會議資料失敗:', error);
    meetings.value = []; // 發生錯誤時清空會議資料
  }
};

// 獲取特定會議室和時間的會議
const getMeetingsForRoomAndHour = (roomId, hour) => {
  if (!meetings.value || meetings.value.length === 0) return [];
  
  // 將會議室ID轉換為字符串，並移除可能的前綴 "T"
  const normalizedRoomId = String(roomId).replace(/^T/, '');
  
  return meetings.value.filter(meeting => {
    if (!meeting || !meeting.roomId || !meeting.startTime || !meeting.endTime) {
      return false;
    }
    
    // 將API返回的會議室ID也標準化處理
    const normalizedMeetingRoomId = String(meeting.roomId).replace(/^T/, '');
    
    // 檢查會議室ID是否匹配
    const roomMatch = normalizedMeetingRoomId === normalizedRoomId;
    
    // 檢查會議日期是否為當前選擇的日期
    const meetingDate = meeting.startTime.split(' ')[0];
    const dateMatch = meetingDate === selectedDate.value;
    
    // 解析會議的開始和結束時間
    const [startHour, startMinute] = meeting.startTime.split(' ')[1].split(':').map(Number);
    const [endHour, endMinute] = meeting.endTime.split(' ')[1].split(':').map(Number);
    
    // 計算會議開始和結束的小時（包含分鐘的小數部分）
    const startHourDecimal = startHour + (startMinute / 60);
    const endHourDecimal = endHour + (endMinute / 60);
    
    // 檢查當前小時是否包含會議的開始時間
    const isStartingHour = Math.floor(startHourDecimal) === hour;
    
    return roomMatch && dateMatch && isStartingHour;
  });
};

// 計算會議持續的小時數（包含分鐘的小數部分）
const getMeetingDuration = (meeting) => {
  if (!meeting || !meeting.startTime || !meeting.endTime) return 1;
  
  // 解析會議的開始和結束時間
  const [startHour, startMinute] = meeting.startTime.split(' ')[1].split(':').map(Number);
  const [endHour, endMinute] = meeting.endTime.split(' ')[1].split(':').map(Number);
  
  // 計算會議開始和結束的小時（包含分鐘的小數部分）
  const startHourDecimal = startHour + (startMinute / 60);
  const endHourDecimal = endHour + (endMinute / 60);
  
  // 如果結束時間小於開始時間，假設跨天，加上24小時
  const duration = endHourDecimal > startHourDecimal ? 
                   endHourDecimal - startHourDecimal : 
                   (endHourDecimal + 24) - startHourDecimal;
  
  return duration; // 返回實際持續時間，不再向上取整
};

// 計算會議在當前小時的頂部偏移量（基於分鐘）
const getMeetingTopOffset = (meeting) => {
  if (!meeting || !meeting.startTime) return 0;
  
  // 獲取會議開始的分鐘
  const startMinute = parseInt(meeting.startTime.split(' ')[1].split(':')[1]);
  
  // 計算頂部偏移量（分鐘佔小時的百分比）
  return (startMinute / 60) * 100;
};

// 計算會議的高度（基於持續時間）
const getMeetingHeight = (meeting) => {
  if (!meeting || !meeting.startTime || !meeting.endTime) return 100;
  
  // 解析會議的開始和結束時間
  const [startHour, startMinute] = meeting.startTime.split(' ')[1].split(':').map(Number);
  const [endHour, endMinute] = meeting.endTime.split(' ')[1].split(':').map(Number);
  
  // 計算會議開始和結束的小時（包含分鐘的小數部分）
  const startHourDecimal = startHour + (startMinute / 60);
  const endHourDecimal = endHour + (endMinute / 60);
  
  // 計算會議持續時間（小時）
  const duration = endHourDecimal > startHourDecimal ? 
                   endHourDecimal - startHourDecimal : 
                   (endHourDecimal + 24) - startHourDecimal;
  
  // 計算會議高度（持續時間佔總高度的百分比）
  return duration * 100;
};

// 檢查特定會議室和時間是否有跨時間段的會議
const hasSpanningMeeting = (roomId, hour) => {
  if (!meetings.value || meetings.value.length === 0) return false;
  
  // 將會議室ID轉換為字符串，並移除可能的前綴 "T"
  const normalizedRoomId = String(roomId).replace(/^T/, '');
  
  return meetings.value.some(meeting => {
    if (!meeting || !meeting.roomId || !meeting.startTime || !meeting.endTime) {
      return false;
    }
    
    // 將API返回的會議室ID也標準化處理
    const normalizedMeetingRoomId = String(meeting.roomId).replace(/^T/, '');
    
    // 檢查會議室ID是否匹配
    const roomMatch = normalizedMeetingRoomId === normalizedRoomId;
    
    // 檢查會議日期是否為當前選擇的日期
    const meetingDate = meeting.startTime.split(' ')[0];
    const dateMatch = meetingDate === selectedDate.value;
    
    // 解析會議的開始和結束時間
    const [startHour, startMinute] = meeting.startTime.split(' ')[1].split(':').map(Number);
    const [endHour, endMinute] = meeting.endTime.split(' ')[1].split(':').map(Number);
    
    // 計算會議開始和結束的小時（包含分鐘的小數部分）
    const startHourDecimal = startHour + (startMinute / 60);
    const endHourDecimal = endHour + (endMinute / 60);
    
    // 檢查當前小時是否在會議時間範圍內，但不是起始小時
    // 修改：確保跨時間段的會議不會阻止點擊
    const isInMeetingRange = hour > Math.floor(startHourDecimal) && hour < Math.ceil(endHourDecimal);
    
    return roomMatch && dateMatch && isInMeetingRange;
  });
};

// 根據ID獲取會議室名稱
const getRoomNameById = (roomId) => {
  // 首先檢查會議資料中是否有對應的roomName
  const meeting = meetings.value.find(m => m.roomId === roomId);
  if (meeting && meeting.roomName) {
    return meeting.roomName;
  }
  
  // 如果會議資料中沒有roomName，則從props.selectedRooms中查找
  if (!props.selectedRooms) return '未知會議室';
  
  // 標準化處理會議室ID
  const normalizedRoomId = String(roomId).replace(/^T/, '');
  
  // 嘗試找到匹配的會議室
  for (const room of props.selectedRooms) {
    // 標準化處理選擇的會議室ID
    const normalizedSelectedRoomId = String(room.id).replace(/^T/, '');
    
    if (normalizedSelectedRoomId === normalizedRoomId) {
      return room.name;
    }
  }
  
  return `會議室 ${roomId}`;
};

// 日期導航方法
const navigateDate = (direction) => {
  const date = new Date(selectedDate.value)
  
  if (selectedView.value === '日') {
    // 日視圖：前後移動一天
    date.setDate(date.getDate() + direction)
  } else if (selectedView.value === '週') {
    // 週視圖：前後移動一週
    date.setDate(date.getDate() + (direction * 7))
  } else {
    // 月視圖：前後移動一個月
    date.setMonth(date.getMonth() + direction)
  }
  
  selectedDate.value = date.toISOString().substring(0, 10)
  // 日期變更後重新獲取會議資料
  fetchMeetings();
}

// 判斷是否為當前日期
const isCurrentDay = (day, weekDay) => {
  if (day === 0) return false;
  
  const today = new Date();
  const currentMonth = new Date(selectedDate.value).getMonth();
  const currentYear = new Date(selectedDate.value).getFullYear();
  
  return today.getDate() === day && 
         today.getMonth() === currentMonth && 
         today.getFullYear() === currentYear;
};

// 從月視圖的日期數字獲取完整日期對象
const getDateFromMonthDay = (day) => {
  if (day === 0) return null;
  
  const date = new Date(selectedDate.value);
  date.setDate(day);
  return date;
};

// 打開會議預約彈窗前先清空前次資料
const openModal = (room, hour, day = null) => {
  // 重置模態框數據
  selectedRoom.value = room;
  selectedHour.value = hour;
  selectedDay.value = day;
  
  // 設置會議室ID
  meetingRoomId.value = room ? room.id : '';
  
  // 設置會議日期
  if (day) {
    // 如果提供了具體日期（週視圖或月視圖），使用該日期
    meetingStartDate.value = formatDate(day);
    meetingEndDate.value = formatDate(day);
  } else {
    // 否則使用當前選擇的日期（日視圖）
    meetingStartDate.value = selectedDate.value;
    meetingEndDate.value = selectedDate.value;
  }
  
  // 設置預設的開始時間
  if (hour !== null) {
    meetingStartHour.value = hour.toString().padStart(2, '0');
    meetingStartMinute.value = '00';
    
    // 設置預設的結束時間（開始時間 + 1小時）
    const endHour = (hour + 1) % 24;
    meetingEndHour.value = endHour.toString().padStart(2, '0');
    meetingEndMinute.value = '00';
  } else {
    // 如果沒有指定時間，使用當前時間
    const now = new Date();
    meetingStartHour.value = now.getHours().toString().padStart(2, '0');
    meetingStartMinute.value = '00';
    meetingEndHour.value = ((now.getHours() + 1) % 24).toString().padStart(2, '0');
    meetingEndMinute.value = '00';
  }
  
  // 重置視圖模式
  isViewMode.value = false;
  currentMeetingId.value = '';
  currentMeetingDetails.value = {};
  
  // 直接打開模態框，不再調用 clearAll 方法
  // 讓模態框的 initForm 方法負責初始化數據
  showModal.value = true;
};

// 處理會議預約確認
const onConfirm = (meetingData) => {
  console.log('會議預約確認:', meetingData)
  // 這裡可以添加預約邏輯，例如發送API請求等
  
  // 預約成功後重新獲取會議資料
  fetchMeetings();
}

// 監聽視圖變化，更新日期格式並重新獲取會議資料
watch(selectedView, (newView) => {
  console.log('視圖變更為:', newView);
  fetchMeetings();
})

// 監聽日期變化，重新獲取會議資料
watch(selectedDate, (newDate) => {
  console.log('日期變更為:', newDate);
  fetchMeetings();
})

// 監聽選擇的會議室變化，重新獲取會議資料
watch(() => props.selectedRooms, (newRooms) => {
  console.log('選擇的會議室變更為:', newRooms);
  fetchMeetings();
}, { deep: true })

// 組件掛載時的初始化
onMounted(() => {
  console.log('日曆視圖組件掛載');
  // 初始化時獲取會議資料
  fetchMeetings();
  
  // 多次嘗試滾動，確保至少有一次成功
  nextTick(() => {
    scrollToBusinessHour();
    
    // 再次嘗試，以防第一次失敗
    //setTimeout(scrollToBusinessHour, 1000);
    //setTimeout(scrollToBusinessHour, 2000);
  });
})

// 顯示會議詳情前先清空前次資料
const showMeetingDetails = (meeting) => {
  // 如果會議不開放，則不顯示詳情
  if (meeting.isOpen === false) {
    console.log('此會議不開放查看:', meeting.subject);
    return;
  }
  
  console.log('會議詳情:', meeting);
  
  // 先獲取模態框引用
  const meetingModal = meetingModalRef.value;
  
  // 如果模態框引用存在，先清空前次資料
  if (meetingModal && typeof meetingModal.clearAll === 'function') {
    meetingModal.clearAll();
  }
  
  // 設置查看模式
  isViewMode.value = true;
  
  // 設置當前會議ID
  currentMeetingId.value = meeting.id;
  
  // 設置會議開始日期和時間
  const startDateTime = meeting.startTime.split(' ');
  const startDate = startDateTime[0];
  const startTime = startDateTime[1].split(':');
  
  meetingStartDate.value = startDate;
  meetingStartHour.value = startTime[0];
  meetingStartMinute.value = startTime[1];
  
  // 設置會議結束日期和時間
  const endDateTime = meeting.endTime.split(' ');
  const endDate = endDateTime[0];
  const endTime = endDateTime[1].split(':');
  
  meetingEndDate.value = endDate;
  meetingEndHour.value = endTime[0];
  meetingEndMinute.value = endTime[1];
  
  // 設置會議室ID
  meetingRoomId.value = meeting.roomId;
  
  // 獲取會議詳細資訊
  api.get(`/api/reserve/meeting/${meeting.id}`, {
    callback: function(response) {
      console.log('獲取會議詳情成功:', response.data);
      
      // 設置會議詳情
      currentMeetingDetails.value = response.data;
      
      // 打開預約視窗
      showModal.value = true;
    },
    error: function(error) {
      console.error('獲取會議詳情失敗:', error);
      
      // 如果獲取詳情失敗，使用基本信息
      currentMeetingDetails.value = {
        subject: meeting.subject || '',
        hostName: meeting.hostName || '',
        contactName: meeting.contactName || '',
        contactPhone: meeting.contactPhone || '',
        roomId: meeting.roomId,
        roomName: meeting.roomName || getRoomNameById(meeting.roomId),
        startTime: meeting.startTime,
        endTime: meeting.endTime,
        isPublic: meeting.isPublic !== undefined ? meeting.isPublic : true
      };
      
      // 打開預約視窗
      showModal.value = true;
    }
  });
};

// 獲取特定日期和時間的所有會議
const getMeetingsForDateAndHour = (date, hour) => {
  if (!meetings.value || meetings.value.length === 0) return [];
  
  // 格式化日期為 YYYY-MM-DD 格式
  const formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
  
  return meetings.value.filter(meeting => {
    if (!meeting || !meeting.startTime || !meeting.endTime) {
      return false;
    }
    
    // 檢查會議日期是否匹配
    const meetingDate = meeting.startTime.split(' ')[0];
    const dateMatch = meetingDate === formattedDate;
    
    // 解析會議的開始和結束時間
    const [startHour, startMinute] = meeting.startTime.split(' ')[1].split(':').map(Number);
    const [endHour, endMinute] = meeting.endTime.split(' ')[1].split(':').map(Number);
    
    // 計算會議開始和結束的小時（包含分鐘的小數部分）
    const startHourDecimal = startHour + (startMinute / 60);
    const endHourDecimal = endHour + (endMinute / 60);
    
    // 檢查當前小時是否包含會議的開始時間
    const isStartingHour = Math.floor(startHourDecimal) === hour;
    
    return dateMatch && isStartingHour;
  });
};

// 獲取特定日期和時間的會議數量
const getMeetingsCountForDateAndHour = (date, hour) => {
  return getMeetingsForDateAndHour(date, hour).length || 1; // 至少返回1，避免除以0
};

// 獲取特定會議在同一時段中的索引（用於計算水平位置）
const getMeetingIndexForDateAndHour = (date, hour, targetMeeting) => {
  const meetingsInSameSlot = getMeetingsForDateAndHour(date, hour);
  return meetingsInSameSlot.findIndex(meeting => meeting.id === targetMeeting.id);
};

// 獲取特定月份日期的所有會議
const getMeetingsForMonthDay = (day) => {
  if (!meetings.value || meetings.value.length === 0 || day === 0) return [];
  
  // 從當前選擇的月份和年份創建日期
  const date = new Date(selectedDate.value);
  date.setDate(day);
  
  // 格式化日期為 YYYY-MM-DD 格式
  const formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
  
  // 過濾出該日期的所有會議
  return meetings.value.filter(meeting => {
    if (!meeting || !meeting.startTime) {
      return false;
    }
    
    // 檢查會議日期是否匹配
    const meetingDate = meeting.startTime.split(' ')[0];
    return meetingDate === formattedDate;
  });
};

// 格式化日期為 YYYY-MM-DD 格式
const formatDate = (date) => {
  return date.toISOString().substring(0, 10);
};

// 修改日視圖渲染方法，確保所有單元格都可點擊
const fixDayViewCells = () => {
  // 等待DOM更新後執行
  setTimeout(() => {
    if (selectedView.value !== '日') return;
    
    // 獲取所有會議室單元格
    const cells = document.querySelectorAll('.day-view-container .calendar-table td:not(.time-column)');
    
    cells.forEach(cell => {
      // 確保單元格可點擊
      cell.style.pointerEvents = 'auto';
      
      // 移除可能阻止點擊的覆蓋層
      const overlays = cell.querySelectorAll('.spanning-meeting-overlay');
      overlays.forEach(overlay => overlay.remove());
      
      // 添加透明的可點擊區域
      if (!cell.querySelector('.cell-clickable-area')) {
        const clickableArea = document.createElement('div');
        clickableArea.className = 'cell-clickable-area';
        clickableArea.style.position = 'absolute';
        clickableArea.style.top = '0';
        clickableArea.style.left = '0';
        clickableArea.style.width = '100%';
        clickableArea.style.height = '100%';
        clickableArea.style.zIndex = '1'; // 低於會議單元格
        
        // 獲取行和列信息
        const row = cell.closest('tr');
        const hourCell = row.querySelector('.time-column');
        const hour = hourCell ? parseInt(hourCell.textContent) : null;
        
        // 獲取會議室信息
        const headerIndex = Array.from(cell.parentNode.children).indexOf(cell);
        const roomHeader = document.querySelector(`.calendar-table thead tr th:nth-child(${headerIndex + 1})`);
        const roomName = roomHeader ? roomHeader.textContent.trim() : '';
        
        // 找到對應的會議室對象
        const room = props.selectedRooms.find(r => r.name === roomName);
        
        if (room && hour !== null) {
          // 添加點擊事件
          clickableArea.addEventListener('click', () => {
            openModal(room, hour);
          });
          
          cell.appendChild(clickableArea);
        }
      }
    });
  }, 200);
};

// 在視圖更新後修復單元格
watch(() => selectedView.value, (newView) => {
  if (newView === '日') {
    fixDayViewCells();
  }
});

// 在日期變更後修復單元格
watch(() => selectedDate.value, () => {
  if (selectedView.value === '日') {
    fixDayViewCells();
  }
});

// 在會議數據加載後修復單元格
watch(() => meetings.value, () => {
  if (selectedView.value === '日') {
    fixDayViewCells();
  }
}, { deep: true });

// 在選擇的會議室變更後修復單元格
watch(() => props.selectedRooms, () => {
  if (selectedView.value === '日') {
    fixDayViewCells();
  }
}, { deep: true });

// 在組件掛載後修復單元格
onMounted(() => {
  // 等待DOM完全加載後執行
  setTimeout(() => {
    if (selectedView.value === '日') {
      fixDayViewCells();
    }
  }, 500);
});

// 添加顯示更多會議的方法
const showMoreMeetings = (day) => {
  // 獲取該日期的完整日期對象
  const date = getDateFromMonthDay(day);
  if (!date) return;
  
  // 獲取該日期的所有會議
  const meetings = getMeetingsForMonthDay(day);
  
  // 設置標題和會議列表
  meetingsModalTitle.value = `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日的所有會議`;
  meetingsModalList.value = meetings;
  
  // 顯示更多會議視窗
  showMeetingsModal.value = true;
};

// 添加：週視圖中顯示民國年日期的函數
const formatDayDateToRoc = (date) => {
  if (!date) return '';
  const year = date.getFullYear();
  const rocYear = year - 1911;
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${rocYear}年${month}月${day}日`;
};

// 添加顯示週檢視更多會議的方法
const showMoreMeetingsForDateAndHour = (date, hour) => {
  if (!date) return;
  
  // 獲取該日期和時間的所有會議
  const meetings = getMeetingsForDateAndHour(date, hour);
  
  // 設置標題和會議列表
  meetingsModalTitle.value = `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日 ${hour}:00 的所有會議`;
  meetingsModalList.value = meetings;
  
  // 顯示更多會議視窗
  showMeetingsModal.value = true;
};

// 修正滾動到 08:00 的位置的函數
const scrollToBusinessHour = () => {
  console.log('嘗試滾動到 08:00 的位置，當前視圖:', selectedView.value);
  
  // 使用延遲確保表格完全渲染
  setTimeout(() => {
    // 嘗試多種選擇器找到 08:00 的行
    const targetSelectors = [
      'tr[data-hour="8"]',
      'tbody tr:nth-child(9)',
      'td.time-column:contains("08:00")',
      'td.time-column:contains("8:00")'
    ];
    
    let targetRow = null;
    
    // 嘗試每個選擇器直到找到目標行
    for (const selector of targetSelectors) {
      if (selector.includes(':contains')) {
        // 對於包含文本的選擇器，使用特殊處理
        const timeColumns = document.querySelectorAll('td.time-column');
        for (const col of timeColumns) {
          if (col.textContent.trim() === '08:00' || col.textContent.trim() === '8:00') {
            targetRow = col.parentElement;
            break;
          }
        }
      } else {
        // 標準選擇器
        targetRow = document.querySelector(selector);
      }
      
      if (targetRow) break;
    }
    
    // 如果找到目標行，直接滾動到該位置（不使用平滑滾動）
    if (targetRow) {
      console.log('找到 08:00 的行，滾動到該位置');
      
      // 獲取日曆容器
      const calendarContainer = document.querySelector('.calendar-table-container');
      if (calendarContainer) {
        // 計算目標位置（目標行的頂部位置減去一些偏移量）
        const targetPosition = targetRow.offsetTop - 60; // 減去表頭高度
        
        // 直接滾動到目標位置（不使用平滑滾動）
        calendarContainer.scrollTop = targetPosition;
        
        console.log('直接滾動到位置:', targetPosition);
      }
    } else {
      console.log('找不到 08:00 的行，嘗試固定位置滾動');
      
      // 獲取日曆容器
      const calendarContainer = document.querySelector('.calendar-table-container');
      if (calendarContainer) {
        // 獲取表格的總高度
        const tableHeight = document.querySelector('table')?.offsetHeight || 1440;
        
        // 假設 24 小時平均分配，計算 8 點的大致位置
        const targetPosition = (tableHeight / 24) * 8;
        
        // 直接滾動到目標位置（不使用平滑滾動）
        calendarContainer.scrollTop = targetPosition;
        
        console.log('使用比例計算直接滾動到 08:00，目標位置:', targetPosition);
      }
    }
  }, 300); // 使用較短的延遲
};

// 確保在視圖變化時也滾動到 08:00
watch(selectedView, (newView) => {
  console.log('視圖變更為:', newView);
  fetchMeetings();
  
  // 無論是日視圖還是週視圖，都滾動到 08:00
  nextTick(() => {
    scrollToBusinessHour();
  });
});

// 確保在組件掛載時滾動到 08:00
onMounted(() => {
  initTheme();
  fetchMeetings();
  
  // 添加：頁面載入後滾動到 08:00 的位置
  nextTick(() => {
    scrollToBusinessHour();
  });
});

// 確保在數據加載後再次嘗試滾動
watch(() => meetings.value, () => {
  // 當會議數據加載完成後，無論是日視圖還是週視圖，都嘗試滾動到 08:00
  nextTick(() => {
    scrollToBusinessHour();
  });
}, { deep: true });

// 調整表格佈局 - 處理日視圖的會議室寬度和凍結效果
const adjustTableLayout = () => {
  // 如果不是日視圖，不做任何處理
  if (selectedView.value !== '日') return;
  
  // 獲取表格
  const table = document.querySelector('.day-view-container .calendar-table');
  if (!table) return;
  
  // 強制設置表格寬度為自動，不填滿整個容器
  table.style.width = 'auto';
  
  // 獲取時間列，確保其寬度固定
  const timeColumns = table.querySelectorAll('.time-column');
  timeColumns.forEach(col => {
    col.style.width = '80px';
    col.style.minWidth = '80px';
    col.style.maxWidth = '80px';
    col.style.position = 'sticky';
    col.style.left = '0';
    col.style.zIndex = col.closest('thead') ? '50' : '20';
    col.style.backgroundColor = isDarkMode.value ? '#212529' : '#f8f9fa';
  });
  
  // 獲取表頭行
  const headerRow = table.querySelector('thead tr');
  if (headerRow) {
    const headerCells = headerRow.querySelectorAll('th');
    headerCells.forEach(cell => {
      cell.style.position = 'sticky';
      cell.style.top = '0';
      cell.style.zIndex = cell.classList.contains('time-column') ? '50' : '30';
      cell.style.backgroundColor = isDarkMode.value ? '#212529' : '#f8f9fa';
    });
  }
  
  // 獲取所有會議室標頭
  const roomHeaders = table.querySelectorAll('th:not(.time-column)');
  roomHeaders.forEach(header => {
    header.style.width = '200px';
    header.style.minWidth = '200px';
    header.style.maxWidth = '200px';
  });
  
  // 獲取所有會議室單元格，使其與標頭寬度一致
  const roomCells = table.querySelectorAll('td:not(.time-column)');
  roomCells.forEach(cell => {
    cell.style.width = '200px';
    cell.style.minWidth = '200px';
    cell.style.maxWidth = '200px';
  });
  
  // 確保表格容器可以水平滾動
  const container = document.querySelector('.calendar-table-container');
  if (container) {
    container.style.overflowX = 'auto';
    container.style.overflowY = 'auto';
    container.style.maxHeight = 'calc(100vh - 200px)'; // 設置最大高度，確保可以垂直滾動
    container.style.position = 'relative'; // 為絕對定位的子元素提供參考
  }
};

// 在視圖變更時調整表格佈局
watch(selectedView, () => {
  nextTick(() => {
    adjustTableLayout();
  });
});

// 在會議數據加載後調整表格佈局
watch(() => meetings.value, () => {
  nextTick(() => {
    adjustTableLayout();
  });
}, { deep: true });

// 在選擇的會議室變更後調整表格佈局
watch(() => props.selectedRooms, () => {
  nextTick(() => {
    adjustTableLayout();
  });
}, { deep: true });

// 在組件掛載時調整表格佈局
onMounted(() => {
  nextTick(() => {
    adjustTableLayout();
  });
});

// 添加檢測橫向捲軸和控制捲動的功能
const tableContainer = ref(null);
const hasHorizontalScroll = ref(false);

// 檢查是否有橫向捲軸
const checkHorizontalScroll = () => {
  if (!tableContainer.value) return;
  
  const container = tableContainer.value;
  hasHorizontalScroll.value = container.scrollWidth > container.clientWidth;
};

// 控制表格橫向捲動
const scrollTableHorizontally = (amount) => {
  if (!tableContainer.value) return;
  
  tableContainer.value.scrollLeft += amount;
};

// 監聽視窗大小變化，重新檢查是否有橫向捲軸
onMounted(() => {
  // 初始化時檢查是否有橫向捲軸
  nextTick(() => {
    checkHorizontalScroll();
    
    // 監聽視窗大小變化
    window.addEventListener('resize', checkHorizontalScroll);
    
    // 監聽表格容器的捲動事件
    if (tableContainer.value) {
      tableContainer.value.addEventListener('scroll', checkHorizontalScroll);
    }
    
    // 調整按鈕位置以對齊會議室名稱和時間列之間的線
    adjustButtonPosition();
  });
});

// 在組件卸載時移除事件監聽器
onUnmounted(() => {
  window.removeEventListener('resize', checkHorizontalScroll);
  
  if (tableContainer.value) {
    tableContainer.value.removeEventListener('scroll', checkHorizontalScroll);
  }
});

// 在會議數據變化時重新檢查是否有橫向捲軸
watch(() => meetings.value, () => {
  nextTick(checkHorizontalScroll);
}, { deep: true });

// 在選擇的會議室變化時重新檢查是否有橫向捲軸
watch(() => props.selectedRooms, () => {
  nextTick(checkHorizontalScroll);
}, { deep: true });

// 調整按鈕位置以對齊會議室名稱和時間列之間的線中間
const adjustButtonPosition = () => {
  // 獲取表頭高度
  const headerHeight = document.querySelector('.calendar-table thead th')?.offsetHeight;
  
  if (headerHeight) {
    // 計算按鈕的垂直位置，使其與會議室名稱高度對齊
    const buttonTop = (headerHeight - 36) / 2; // 36是按鈕高度
    
    // 獲取按鈕元素
    const leftButton = document.querySelector('.scroll-left-btn');
    const rightButton = document.querySelector('.scroll-right-btn');
    
    // 設置按鈕的垂直位置
    if (leftButton) leftButton.style.top = `${buttonTop}px`;
    if (rightButton) rightButton.style.top = `${buttonTop}px`;
    
    // 獲取時間列的寬度
    const timeColumn = document.querySelector('.time-column');
    if (timeColumn && leftButton) {
      // 精確調整左側按鈕的水平位置，使其中心點對齊線中間
      const timeColumnWidth = timeColumn.offsetWidth;
      leftButton.style.left = `${timeColumnWidth}px`;
      leftButton.style.transform = 'translateX(-50%)'; // 使按鈕中心點對齊線中間
    }
    
    // 調整右側按鈕的位置
    if (rightButton) {
      rightButton.style.right = '5px'; // 進一步增加與右側的距離
    }
  }
};

// 在視圖變更時也調整按鈕位置
watch(selectedView, () => {
  nextTick(() => {
    adjustButtonPosition();
  });
});

// 在會議數據變化時也調整按鈕位置
watch(() => meetings.value, () => {
  nextTick(() => {
    adjustButtonPosition();
  });
}, { deep: true });
</script>
