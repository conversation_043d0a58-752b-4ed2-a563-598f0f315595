<template>
  <div id="app" class="app-content">
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <keep-alive>
          <component :is="Component" :key="$route.fullPath" />
        </keep-alive>
      </transition>
    </router-view>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, watch } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

// 監視路由變化
watch(() => route.fullPath, (newPath, oldPath) => {
  console.log(`路由變化: 從 ${oldPath} 到 ${newPath}`);
  
  // 強制重新計算佈局
  setTimeout(() => {
    document.body.offsetHeight;
  }, 100);
});

// 處理頁面載入和卸載
onMounted(() => {
  // 確保頁面已完全載入
  if (document.readyState === 'complete') {
    document.documentElement.classList.add('app-loaded');
  } else {
    window.addEventListener('load', () => {
      document.documentElement.classList.add('app-loaded');
    });
  }
  
  // 添加視窗大小變化監聽器
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  // 移除視窗大小變化監聽器
  window.removeEventListener('resize', handleResize);
});

// 處理視窗大小變化
function handleResize() {
  // 強制重新計算佈局
  document.body.offsetHeight;
}
</script>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 確保頁面在應用載入前不顯示內容，避免跑版 */
html:not(.app-loaded) .app-content {
  visibility: hidden;
}

/* 修復跑版問題的全局樣式 */
.app-loaded .app-content {
  visibility: visible;
  transition: opacity 0.3s ease;
}
</style>

