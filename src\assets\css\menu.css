
/* Menu Container */
.menu {
	position: absolute;
	inset: 2px 1650px 2px 0;
	width: 270px;
	border-color: rgb(255, 255, 255, 0.098);
	border-style: solid;
	border-width: 0 1px 0 0;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	padding: 2px;
	box-sizing: border-box;
}

/* Content Container */
.menu_Content {
	position: fixed;
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 20px;
	padding: 50px 0 0 20px;
}

/* Logo */
.menu_logo {
	width: 200px;
	margin-bottom: 48px;
}

/* Common Styles for Buttons */
.menuBtn {
	display: flex;
	align-items: center;
	gap: 10px;
	width: 210px;
	padding: 15px;
	box-sizing: border-box;
	border: none;
	background: none;
	cursor: pointer;
	border-radius: 10px;
	transform: translateY(-2px);
	text-decoration: none;
	:hover{
		text-decoration: none;
	}
}

.menuBtn-active {
	background-color: rgba(255, 255, 255, 0.1);
}

.menuBtn:hover {
	background-color: rgba(255, 255, 255, 0.1);
}

.menuBtn_img{
	width: 30px;
	height: 30px;
}

.menuBtn_txt{
	font-size: 18px;
	font-weight: 400;
	color: rgb(255, 255, 255);
	text-align: center !important;
}

.user {
	position: fixed; 
	bottom: 20px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 10px;
	width: 210px;
	padding: 15px;
	box-sizing: border-box;
	border: none;
	background: linear-gradient(to right, rgb(57, 67, 89), rgb(35, 39, 49));
	cursor: pointer;
	border-radius: 10px;
	left: 0%;
}

.user:hover {
	background: linear-gradient(to right, rgb(57, 67, 89, 0.5), rgb(35, 39, 49, 0.5));
	.user_txt {
		color: rgb(255, 255, 255, 0.95);
	}
}

.user_img {
	width: 40px;
	height: 40px;
	background-size: cover;
	border-radius: 50%;
}

.user_txt {
	font-size: 16px;
	font-weight: 500;
	color: rgb(255, 255, 255);
}
